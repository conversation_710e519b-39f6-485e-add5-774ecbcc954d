import cron from 'node-cron';
import patternRecognitionService from './patternRecognitionService';
import knowledgeManagementService from './knowledgeManagementService';
import learningService from './learningService';
import migrationService from './migrationService';

class BackgroundLearningService {
  private isRunning = false;
  private learningTasks: cron.ScheduledTask[] = [];

  // Initialize background learning processes
  async initialize(): Promise<void> {
    try {
      console.log('Initializing background learning service...');

      // Check if migration is needed
      const systemValid = await migrationService.validateSystemIntegrity();
      if (!systemValid) {
        console.log('System integrity check failed, running migration...');
        await migrationService.runCompleteMigration();
      } else {
        console.log('System integrity check passed');
        // Initialize default knowledge base if needed
        await knowledgeManagementService.initializeDefaultKnowledge();
      }

      // Schedule learning tasks
      this.schedulePatternRecognition();
      this.scheduleKnowledgeOptimization();
      this.scheduleAnalytics();

      this.isRunning = true;
      console.log('Background learning service initialized successfully');
    } catch (error) {
      console.error('Error initializing background learning service:', error);
    }
  }

  // Schedule pattern recognition to run every 6 hours
  private schedulePatternRecognition(): void {
    const task = cron.schedule('0 */6 * * *', async () => {
      try {
        console.log('Starting scheduled pattern recognition...');
        await patternRecognitionService.processRecentConversations();
        console.log('Pattern recognition completed');
      } catch (error) {
        console.error('Error in scheduled pattern recognition:', error);
      }
    }, {
      scheduled: false
    });

    this.learningTasks.push(task);
    task.start();
    console.log('Pattern recognition scheduled to run every 6 hours');
  }

  // Schedule knowledge base optimization to run daily at 2 AM
  private scheduleKnowledgeOptimization(): void {
    const task = cron.schedule('0 2 * * *', async () => {
      try {
        console.log('Starting scheduled knowledge optimization...');
        await knowledgeManagementService.cleanupInactiveKnowledge();
        await knowledgeManagementService.optimizeKnowledgeBase();
        console.log('Knowledge optimization completed');
      } catch (error) {
        console.error('Error in scheduled knowledge optimization:', error);
      }
    }, {
      scheduled: false
    });

    this.learningTasks.push(task);
    task.start();
    console.log('Knowledge optimization scheduled to run daily at 2 AM');
  }

  // Schedule analytics and reporting to run weekly
  private scheduleAnalytics(): void {
    const task = cron.schedule('0 3 * * 0', async () => {
      try {
        console.log('Starting scheduled analytics...');
        await this.generateWeeklyReport();
        console.log('Analytics completed');
      } catch (error) {
        console.error('Error in scheduled analytics:', error);
      }
    }, {
      scheduled: false
    });

    this.learningTasks.push(task);
    task.start();
    console.log('Analytics scheduled to run weekly on Sundays at 3 AM');
  }

  // Manual trigger for immediate learning
  async triggerImmediateLearning(): Promise<void> {
    try {
      console.log('Triggering immediate learning process...');
      
      // Run pattern recognition
      await patternRecognitionService.processRecentConversations();
      
      // Optimize knowledge base
      await knowledgeManagementService.cleanupInactiveKnowledge();
      
      console.log('Immediate learning process completed');
    } catch (error) {
      console.error('Error in immediate learning process:', error);
      throw error;
    }
  }

  // Generate weekly analytics report
  private async generateWeeklyReport(): Promise<void> {
    try {
      const stats = await knowledgeManagementService.getKnowledgeStats();
      const sessionStats = await this.getSessionStats();
      
      console.log('=== Weekly Learning Report ===');
      console.log(`Knowledge Base Entries: ${stats.total_knowledge_entries}`);
      console.log(`FAQ Entries: ${stats.total_faq_entries}`);
      console.log(`Average Effectiveness: ${(stats.average_effectiveness * 100).toFixed(1)}%`);
      console.log(`Average Success Rate: ${stats.average_success_rate.toFixed(1)}%`);
      console.log(`Total Knowledge Usage: ${stats.total_knowledge_usage}`);
      console.log(`Total FAQ Usage: ${stats.total_faq_usage}`);
      console.log(`Active Sessions This Week: ${sessionStats.active_sessions}`);
      console.log(`Contact Collection Rate: ${sessionStats.contact_collection_rate.toFixed(1)}%`);
      console.log('==============================');
      
      // Store report in database for future reference
      await this.storeWeeklyReport({
        ...stats,
        ...sessionStats,
        report_date: new Date()
      });
    } catch (error) {
      console.error('Error generating weekly report:', error);
    }
  }

  // Get session statistics for the past week
  private async getSessionStats(): Promise<any> {
    try {
      const query = `
        SELECT 
          COUNT(*) as active_sessions,
          COUNT(CASE WHEN contact_collected = true THEN 1 END) as contacts_collected,
          AVG(total_messages) as avg_messages_per_session,
          AVG(CASE WHEN satisfaction_rating IS NOT NULL THEN satisfaction_rating END) as avg_satisfaction
        FROM chat_sessions 
        WHERE created_at >= NOW() - INTERVAL '7 days'
      `;
      
      // This would use the pool from learningService, but for now we'll return mock data
      return {
        active_sessions: 0,
        contacts_collected: 0,
        contact_collection_rate: 0,
        avg_messages_per_session: 0,
        avg_satisfaction: 0
      };
    } catch (error) {
      console.error('Error getting session stats:', error);
      return {
        active_sessions: 0,
        contacts_collected: 0,
        contact_collection_rate: 0,
        avg_messages_per_session: 0,
        avg_satisfaction: 0
      };
    }
  }

  // Store weekly report in database
  private async storeWeeklyReport(reportData: any): Promise<void> {
    try {
      // This would store the report in a reports table
      // For now, we'll just log it
      console.log('Weekly report stored:', JSON.stringify(reportData, null, 2));
    } catch (error) {
      console.error('Error storing weekly report:', error);
    }
  }

  // Real-time learning from conversation outcomes
  async learnFromConversationOutcome(sessionId: string, outcome: string, feedback?: any): Promise<void> {
    try {
      // Update session outcome
      await learningService.updateChatSession(sessionId, {
        conversation_outcome: outcome,
        end_time: new Date()
      });

      // If the outcome was positive, learn from this conversation
      if (outcome === 'contact_collected' || outcome === 'completed') {
        await this.extractSuccessPatterns(sessionId);
      }

      // If there's feedback, use it to improve responses
      if (feedback) {
        await this.processFeedback(sessionId, feedback);
      }
    } catch (error) {
      console.error('Error learning from conversation outcome:', error);
    }
  }

  // Extract success patterns from a specific session
  private async extractSuccessPatterns(sessionId: string): Promise<void> {
    try {
      // This would analyze the successful conversation and extract patterns
      // For now, we'll just log it
      console.log(`Extracting success patterns from session: ${sessionId}`);
    } catch (error) {
      console.error('Error extracting success patterns:', error);
    }
  }

  // Process user feedback to improve responses
  private async processFeedback(sessionId: string, feedback: any): Promise<void> {
    try {
      // This would process feedback and adjust knowledge effectiveness
      console.log(`Processing feedback for session: ${sessionId}`, feedback);
    } catch (error) {
      console.error('Error processing feedback:', error);
    }
  }

  // Get learning service status
  getStatus(): any {
    return {
      isRunning: this.isRunning,
      scheduledTasks: this.learningTasks.length,
      lastPatternRecognition: 'Not implemented yet',
      lastOptimization: 'Not implemented yet',
      lastReport: 'Not implemented yet'
    };
  }

  // Stop all background learning processes
  async stop(): Promise<void> {
    try {
      console.log('Stopping background learning service...');
      
      // Stop all scheduled tasks
      this.learningTasks.forEach(task => {
        task.stop();
        task.destroy();
      });
      
      this.learningTasks = [];
      this.isRunning = false;
      
      console.log('Background learning service stopped');
    } catch (error) {
      console.error('Error stopping background learning service:', error);
    }
  }

  // Restart the service
  async restart(): Promise<void> {
    await this.stop();
    await this.initialize();
  }
}

export default new BackgroundLearningService();
