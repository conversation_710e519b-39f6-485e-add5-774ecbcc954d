import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// PostgreSQL connection configuration
const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'postgres',
  password: process.env.POSTGRES_PASSWORD || '',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
});

export interface ResponseEffectiveness {
  id?: number;
  session_id: string;
  message_id: number;
  response_type: string;
  user_feedback?: string;
  conversation_continued: boolean;
  led_to_contact_collection: boolean;
  response_rating?: number;
  time_to_next_message?: number;
  user_satisfaction_indicator?: string;
  outcome_achieved: boolean;
}

export interface EffectivenessMetrics {
  total_responses: number;
  successful_responses: number;
  success_rate: number;
  avg_response_rating: number;
  contact_collection_rate: number;
  conversation_continuation_rate: number;
  avg_time_to_next_message: number;
}

class EffectivenessTrackingService {
  // Track response effectiveness
  async trackResponseEffectiveness(effectiveness: ResponseEffectiveness): Promise<number> {
    try {
      const query = `
        INSERT INTO response_effectiveness 
        (session_id, message_id, response_type, user_feedback, conversation_continued, 
         led_to_contact_collection, response_rating, time_to_next_message, 
         user_satisfaction_indicator, outcome_achieved)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        RETURNING id
      `;
      
      const values = [
        effectiveness.session_id,
        effectiveness.message_id,
        effectiveness.response_type,
        effectiveness.user_feedback || null,
        effectiveness.conversation_continued,
        effectiveness.led_to_contact_collection,
        effectiveness.response_rating || null,
        effectiveness.time_to_next_message || null,
        effectiveness.user_satisfaction_indicator || null,
        effectiveness.outcome_achieved
      ];

      const result = await pool.query(query, values);
      return result.rows[0].id;
    } catch (error) {
      console.error('Error tracking response effectiveness:', error);
      throw error;
    }
  }

  // Analyze conversation flow to determine effectiveness
  async analyzeConversationEffectiveness(sessionId: string): Promise<void> {
    try {
      // Get all messages in the conversation
      const messagesQuery = `
        SELECT id, message_order, role, content, response_time_ms, timestamp
        FROM conversation_messages 
        WHERE session_id = $1 
        ORDER BY message_order
      `;
      
      const messages = await pool.query(messagesQuery, [sessionId]);
      
      if (messages.rows.length < 2) return; // Need at least user message and response
      
      // Analyze each assistant response
      for (let i = 0; i < messages.rows.length; i++) {
        const message = messages.rows[i];
        
        if (message.role === 'assistant') {
          const prevMessage = messages.rows[i - 1];
          const nextMessage = messages.rows[i + 1];
          
          // Determine if conversation continued
          const conversationContinued = nextMessage && nextMessage.role === 'user';
          
          // Calculate time to next message
          let timeToNext = null;
          if (nextMessage) {
            timeToNext = new Date(nextMessage.timestamp).getTime() - new Date(message.timestamp).getTime();
          }
          
          // Determine response type
          const responseType = this.categorizeResponse(message.content);
          
          // Check if this led to contact collection
          const ledToContact = await this.checkContactCollection(sessionId, message.message_order);
          
          // Determine satisfaction indicators
          const satisfactionIndicator = this.analyzeSatisfactionIndicators(nextMessage?.content || '');
          
          // Track effectiveness
          await this.trackResponseEffectiveness({
            session_id: sessionId,
            message_id: message.id,
            response_type: responseType,
            conversation_continued: conversationContinued,
            led_to_contact_collection: ledToContact,
            time_to_next_message: timeToNext,
            user_satisfaction_indicator: satisfactionIndicator,
            outcome_achieved: conversationContinued || ledToContact
          });
        }
      }
    } catch (error) {
      console.error('Error analyzing conversation effectiveness:', error);
    }
  }

  // Get effectiveness metrics for a specific time period
  async getEffectivenessMetrics(days: number = 7): Promise<EffectivenessMetrics> {
    try {
      const query = `
        SELECT 
          COUNT(*) as total_responses,
          COUNT(CASE WHEN outcome_achieved = true THEN 1 END) as successful_responses,
          AVG(CASE WHEN response_rating IS NOT NULL THEN response_rating END) as avg_response_rating,
          COUNT(CASE WHEN led_to_contact_collection = true THEN 1 END) as contact_collections,
          COUNT(CASE WHEN conversation_continued = true THEN 1 END) as continued_conversations,
          AVG(CASE WHEN time_to_next_message IS NOT NULL THEN time_to_next_message END) as avg_time_to_next
        FROM response_effectiveness 
        WHERE created_at >= NOW() - INTERVAL '${days} days'
      `;
      
      const result = await pool.query(query);
      const row = result.rows[0];
      
      const totalResponses = parseInt(row.total_responses) || 0;
      const successfulResponses = parseInt(row.successful_responses) || 0;
      const contactCollections = parseInt(row.contact_collections) || 0;
      const continuedConversations = parseInt(row.continued_conversations) || 0;
      
      return {
        total_responses: totalResponses,
        successful_responses: successfulResponses,
        success_rate: totalResponses > 0 ? (successfulResponses / totalResponses) * 100 : 0,
        avg_response_rating: parseFloat(row.avg_response_rating) || 0,
        contact_collection_rate: totalResponses > 0 ? (contactCollections / totalResponses) * 100 : 0,
        conversation_continuation_rate: totalResponses > 0 ? (continuedConversations / totalResponses) * 100 : 0,
        avg_time_to_next_message: parseFloat(row.avg_time_to_next) || 0
      };
    } catch (error) {
      console.error('Error getting effectiveness metrics:', error);
      return {
        total_responses: 0,
        successful_responses: 0,
        success_rate: 0,
        avg_response_rating: 0,
        contact_collection_rate: 0,
        conversation_continuation_rate: 0,
        avg_time_to_next_message: 0
      };
    }
  }

  // Get effectiveness by response type
  async getEffectivenessByType(days: number = 7): Promise<any[]> {
    try {
      const query = `
        SELECT 
          response_type,
          COUNT(*) as total_responses,
          COUNT(CASE WHEN outcome_achieved = true THEN 1 END) as successful_responses,
          AVG(CASE WHEN response_rating IS NOT NULL THEN response_rating END) as avg_rating,
          COUNT(CASE WHEN led_to_contact_collection = true THEN 1 END) as contact_collections
        FROM response_effectiveness 
        WHERE created_at >= NOW() - INTERVAL '${days} days'
        GROUP BY response_type
        ORDER BY successful_responses DESC
      `;
      
      const result = await pool.query(query);
      
      return result.rows.map(row => ({
        response_type: row.response_type,
        total_responses: parseInt(row.total_responses),
        successful_responses: parseInt(row.successful_responses),
        success_rate: parseInt(row.total_responses) > 0 ? 
          (parseInt(row.successful_responses) / parseInt(row.total_responses)) * 100 : 0,
        avg_rating: parseFloat(row.avg_rating) || 0,
        contact_collections: parseInt(row.contact_collections)
      }));
    } catch (error) {
      console.error('Error getting effectiveness by type:', error);
      return [];
    }
  }

  // Update response effectiveness based on user feedback
  async updateEffectivenessFromFeedback(messageId: number, feedback: any): Promise<void> {
    try {
      const query = `
        UPDATE response_effectiveness 
        SET user_feedback = $2,
            response_rating = $3,
            outcome_achieved = CASE WHEN $3 >= 4 THEN true ELSE outcome_achieved END,
            user_satisfaction_indicator = $4
        WHERE message_id = $1
      `;
      
      const values = [
        messageId,
        feedback.comment || null,
        feedback.rating || null,
        feedback.satisfaction || null
      ];
      
      await pool.query(query, values);
    } catch (error) {
      console.error('Error updating effectiveness from feedback:', error);
    }
  }

  // Helper methods
  private categorizeResponse(content: string): string {
    const lowerContent = content.toLowerCase();
    
    if (lowerContent.includes('contact') || lowerContent.includes('email') || lowerContent.includes('phone')) {
      return 'contact_information';
    } else if (lowerContent.includes('service') || lowerContent.includes('offer')) {
      return 'service_description';
    } else if (lowerContent.includes('price') || lowerContent.includes('cost')) {
      return 'pricing_information';
    } else if (lowerContent.includes('how') || lowerContent.includes('process')) {
      return 'process_explanation';
    } else if (lowerContent.includes('thank') || lowerContent.includes('welcome')) {
      return 'courtesy_response';
    } else if (lowerContent.includes('help') || lowerContent.includes('assist')) {
      return 'assistance_offer';
    } else {
      return 'general_response';
    }
  }

  private async checkContactCollection(sessionId: string, messageOrder: number): Promise<boolean> {
    try {
      // Check if contact was collected within 5 messages after this response
      const query = `
        SELECT COUNT(*) as contact_count
        FROM chatbot_contacts 
        WHERE chat_session_id = $1 
        AND created_at >= (
          SELECT timestamp FROM conversation_messages 
          WHERE session_id = $1 AND message_order = $2
        )
      `;
      
      const result = await pool.query(query, [sessionId, messageOrder]);
      return parseInt(result.rows[0].contact_count) > 0;
    } catch (error) {
      console.error('Error checking contact collection:', error);
      return false;
    }
  }

  private analyzeSatisfactionIndicators(userResponse: string): string {
    if (!userResponse) return 'unknown';
    
    const lowerResponse = userResponse.toLowerCase();
    
    // Positive indicators
    if (lowerResponse.includes('thank') || lowerResponse.includes('great') || 
        lowerResponse.includes('perfect') || lowerResponse.includes('excellent') ||
        lowerResponse.includes('helpful')) {
      return 'positive';
    }
    
    // Negative indicators
    if (lowerResponse.includes('no') || lowerResponse.includes('not helpful') ||
        lowerResponse.includes('wrong') || lowerResponse.includes('bad') ||
        lowerResponse.includes('terrible')) {
      return 'negative';
    }
    
    // Neutral/continuation indicators
    if (lowerResponse.includes('ok') || lowerResponse.includes('sure') ||
        lowerResponse.includes('yes') || lowerResponse.includes('continue')) {
      return 'neutral';
    }
    
    return 'unknown';
  }

  // Get top performing responses
  async getTopPerformingResponses(limit: number = 10): Promise<any[]> {
    try {
      const query = `
        SELECT 
          cm.content as response_content,
          re.response_type,
          COUNT(*) as usage_count,
          AVG(CASE WHEN re.outcome_achieved THEN 1.0 ELSE 0.0 END) as success_rate,
          AVG(CASE WHEN re.response_rating IS NOT NULL THEN re.response_rating END) as avg_rating
        FROM response_effectiveness re
        JOIN conversation_messages cm ON re.message_id = cm.id
        WHERE re.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY cm.content, re.response_type
        HAVING COUNT(*) >= 3
        ORDER BY success_rate DESC, avg_rating DESC
        LIMIT $1
      `;
      
      const result = await pool.query(query, [limit]);
      
      return result.rows.map(row => ({
        response_content: row.response_content,
        response_type: row.response_type,
        usage_count: parseInt(row.usage_count),
        success_rate: parseFloat(row.success_rate) * 100,
        avg_rating: parseFloat(row.avg_rating) || 0
      }));
    } catch (error) {
      console.error('Error getting top performing responses:', error);
      return [];
    }
  }
}

export default new EffectivenessTrackingService();
