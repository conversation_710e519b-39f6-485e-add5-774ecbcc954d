import knowledgeManagementService from './knowledgeManagementService';
import learningService from './learningService';

class MigrationService {
  // Migrate existing hard-coded knowledge to database
  async migrateExistingKnowledge(): Promise<void> {
    try {
      console.log('Starting migration of existing knowledge...');
      
      // Service information from the chat API
      const serviceKnowledge = [
        {
          question: "What remote IT support services do you offer?",
          answer: "SpireLab provides comprehensive 24/7 helpdesk services, remote troubleshooting, and system maintenance. Our remote IT support ensures your systems run smoothly with minimal downtime.",
          category: "services",
          keywords: ["remote", "support", "helpdesk", "troubleshooting", "maintenance"],
          variations: [
            "Do you provide remote support?",
            "What kind of remote help do you offer?",
            "Remote IT services",
            "24/7 support"
          ]
        },
        {
          question: "What cybersecurity services does SpireLab provide?",
          answer: "Our cybersecurity services include comprehensive threat protection, security audits, and incident response. We help protect your business from cyber threats with advanced security solutions and monitoring.",
          category: "services",
          keywords: ["cybersecurity", "security", "threat", "protection", "audit"],
          variations: [
            "Do you offer cybersecurity?",
            "Security services",
            "Threat protection",
            "Security audit"
          ]
        },
        {
          question: "What cloud infrastructure services do you offer?",
          answer: "SpireLab offers scalable cloud solutions, migration services, and cloud optimization. We help businesses move to the cloud efficiently and optimize their cloud infrastructure for performance and cost.",
          category: "services",
          keywords: ["cloud", "infrastructure", "migration", "scalable", "optimization"],
          variations: [
            "Cloud services",
            "Cloud migration",
            "Cloud solutions",
            "Infrastructure services"
          ]
        },
        {
          question: "Do you develop custom software?",
          answer: "Yes, we specialize in custom software development including tailored applications, web development, and mobile apps. Our development team creates solutions specifically designed for your business needs.",
          category: "services",
          keywords: ["custom", "software", "development", "applications", "web", "mobile"],
          variations: [
            "Custom development",
            "Software development",
            "Web development",
            "Mobile app development"
          ]
        },
        {
          question: "What network solutions do you provide?",
          answer: "Our network solutions include network design, implementation, and management. We create robust, secure networks that support your business operations and growth.",
          category: "services",
          keywords: ["network", "design", "implementation", "management", "solutions"],
          variations: [
            "Network services",
            "Network design",
            "Network management",
            "Networking solutions"
          ]
        },
        {
          question: "Do you offer managed IT services?",
          answer: "Yes, we provide comprehensive managed IT services including proactive monitoring, maintenance, and support. Our managed services ensure your IT infrastructure operates at peak performance.",
          category: "services",
          keywords: ["managed", "monitoring", "maintenance", "proactive", "infrastructure"],
          variations: [
            "Managed services",
            "IT management",
            "Proactive monitoring",
            "IT maintenance"
          ]
        },
        {
          question: "What DevOps and CI/CD services do you offer?",
          answer: "SpireLab provides DevOps services including automation, continuous integration, and deployment pipelines. We help streamline your development process and improve deployment efficiency.",
          category: "services",
          keywords: ["devops", "automation", "ci/cd", "continuous", "integration", "deployment"],
          variations: [
            "DevOps services",
            "Continuous integration",
            "Deployment automation",
            "CI/CD pipeline"
          ]
        },
        {
          question: "Do you provide data analytics services?",
          answer: "Yes, we offer comprehensive data analytics services including business intelligence, data visualization, and predictive analytics. We help you make data-driven decisions for your business.",
          category: "services",
          keywords: ["data", "analytics", "business", "intelligence", "visualization", "predictive"],
          variations: [
            "Data analytics",
            "Business intelligence",
            "Data visualization",
            "Predictive analytics"
          ]
        },
        {
          question: "What digital transformation services do you offer?",
          answer: "Our digital transformation services include process automation and digital strategy consulting. We help businesses modernize their operations and embrace digital technologies.",
          category: "services",
          keywords: ["digital", "transformation", "automation", "strategy", "consulting"],
          variations: [
            "Digital transformation",
            "Process automation",
            "Digital strategy",
            "Modernization"
          ]
        },
        {
          question: "Do you develop ecommerce solutions?",
          answer: "Yes, we provide comprehensive ecommerce solutions including online store development, payment integration, and optimization. We help businesses establish and grow their online presence.",
          category: "services",
          keywords: ["ecommerce", "online", "store", "payment", "integration", "optimization"],
          variations: [
            "Ecommerce development",
            "Online store",
            "E-commerce solutions",
            "Online shopping"
          ]
        },
        {
          question: "What firewall solutions do you implement?",
          answer: "SpireLab provides professional firewall implementations for leading brands including Sophos, FortiGate, Netgate, and SonicWall. We ensure your network is protected with enterprise-grade security.",
          category: "services",
          keywords: ["firewall", "sophos", "fortigate", "netgate", "sonicwall", "security"],
          variations: [
            "Firewall implementation",
            "Network security",
            "Firewall services",
            "Security appliances"
          ]
        }
      ];

      // Common business questions
      const businessKnowledge = [
        {
          question: "What industries does SpireLab serve?",
          answer: "SpireLab serves businesses across various industries including healthcare, finance, retail, manufacturing, education, and professional services. Our solutions are tailored to meet industry-specific requirements.",
          category: "business",
          keywords: ["industries", "healthcare", "finance", "retail", "manufacturing", "education"],
          variations: [
            "What sectors do you work with?",
            "Which industries do you serve?",
            "Business sectors",
            "Industry experience"
          ]
        },
        {
          question: "How do I get started with SpireLab?",
          answer: "Getting started is easy! Contact us for a free consultation where we'll discuss your needs and provide a customized solution. You can reach us via email, phone, or our contact form.",
          category: "process",
          keywords: ["get started", "consultation", "free", "contact", "customized"],
          variations: [
            "How to begin?",
            "Starting process",
            "First steps",
            "How to start"
          ]
        },
        {
          question: "Do you provide 24/7 support?",
          answer: "Yes, we offer 24/7 support for our managed services clients. Our support team is available around the clock to ensure your systems remain operational and any issues are resolved quickly.",
          category: "support",
          keywords: ["24/7", "support", "managed", "available", "operational"],
          variations: [
            "Round the clock support",
            "Always available support",
            "24 hour support",
            "Continuous support"
          ]
        },
        {
          question: "What makes SpireLab different from other IT companies?",
          answer: "SpireLab stands out through our personalized approach, comprehensive service offerings, and commitment to long-term partnerships. We focus on understanding your business needs and delivering tailored solutions that drive growth.",
          category: "business",
          keywords: ["different", "personalized", "comprehensive", "partnerships", "tailored"],
          variations: [
            "Why choose SpireLab?",
            "What sets you apart?",
            "Competitive advantages",
            "Unique features"
          ]
        }
      ];

      // Migrate service knowledge
      console.log('Migrating service knowledge...');
      for (const knowledge of serviceKnowledge) {
        await knowledgeManagementService.addFAQEntry({
          question: knowledge.question,
          answer: knowledge.answer,
          category: knowledge.category,
          keywords: knowledge.keywords,
          variations: knowledge.variations,
          confidence_score: 1.0,
          is_active: true
        });
      }

      // Migrate business knowledge
      console.log('Migrating business knowledge...');
      for (const knowledge of businessKnowledge) {
        await knowledgeManagementService.addFAQEntry({
          question: knowledge.question,
          answer: knowledge.answer,
          category: knowledge.category,
          keywords: knowledge.keywords,
          variations: knowledge.variations,
          confidence_score: 1.0,
          is_active: true
        });
      }

      console.log(`Successfully migrated ${serviceKnowledge.length + businessKnowledge.length} knowledge entries`);
    } catch (error) {
      console.error('Error migrating existing knowledge:', error);
      throw error;
    }
  }

  // Create sample conversation data for testing
  async createSampleConversations(): Promise<void> {
    try {
      console.log('Creating sample conversations for testing...');
      
      const sampleConversations = [
        {
          sessionId: 'sample-001',
          customerIdentifier: 'test-customer-1',
          messages: [
            { role: 'user', content: 'Hello, what services do you offer?' },
            { role: 'assistant', content: 'SpireLab offers comprehensive IT solutions including web development, mobile app development, cloud solutions, AI/ML services, cybersecurity, and digital transformation consulting.' },
            { role: 'user', content: 'I need help with cybersecurity' },
            { role: 'assistant', content: 'Our cybersecurity services include comprehensive threat protection, security audits, and incident response. We help protect your business from cyber threats with advanced security solutions and monitoring.' },
            { role: 'user', content: 'How can I contact you?' },
            { role: 'assistant', content: 'You can contact SpireLab through multiple channels: Email <NAME_EMAIL>, call us at ******-SPIRE-LAB, or fill out our contact form on the website.' }
          ],
          outcome: 'contact_collected'
        },
        {
          sessionId: 'sample-002',
          customerIdentifier: 'test-customer-2',
          messages: [
            { role: 'user', content: 'Do you do custom software development?' },
            { role: 'assistant', content: 'Yes, we specialize in custom software development including tailored applications, web development, and mobile apps. Our development team creates solutions specifically designed for your business needs.' },
            { role: 'user', content: 'What about pricing?' },
            { role: 'assistant', content: 'Our pricing varies based on project scope, complexity, and timeline. We offer flexible pricing models including fixed-price projects, hourly rates, and retainer agreements. Contact us for a free consultation and customized quote.' }
          ],
          outcome: 'completed'
        },
        {
          sessionId: 'sample-003',
          customerIdentifier: 'test-customer-3',
          messages: [
            { role: 'user', content: 'Hi there' },
            { role: 'assistant', content: 'Hello! Welcome to SpireLab. How can I help you today?' },
            { role: 'user', content: 'Just looking around' },
            { role: 'assistant', content: 'That\'s great! Feel free to ask me any questions about our IT solutions and services. I\'m here to help!' }
          ],
          outcome: 'abandoned'
        }
      ];

      for (const conversation of sampleConversations) {
        // Create session
        await learningService.createChatSession({
          session_id: conversation.sessionId,
          customer_identifier: conversation.customerIdentifier,
          conversation_outcome: conversation.outcome,
          contact_collected: conversation.outcome === 'contact_collected'
        });

        // Add messages
        for (let i = 0; i < conversation.messages.length; i++) {
          const message = conversation.messages[i];
          await learningService.logMessage({
            session_id: conversation.sessionId,
            message_order: i + 1,
            role: message.role as 'user' | 'assistant',
            content: message.content,
            timestamp: new Date(Date.now() - (conversation.messages.length - i) * 60000) // Spread messages over time
          });
        }

        // Update session with final outcome
        await learningService.updateChatSession(conversation.sessionId, {
          conversation_outcome: conversation.outcome,
          total_messages: conversation.messages.length,
          end_time: new Date()
        });
      }

      console.log(`Created ${sampleConversations.length} sample conversations`);
    } catch (error) {
      console.error('Error creating sample conversations:', error);
      throw error;
    }
  }

  // Test the learning system
  async testLearningSystem(): Promise<boolean> {
    try {
      console.log('Testing learning system...');
      
      // Test knowledge search
      const searchResults = await knowledgeManagementService.searchKnowledge('cybersecurity services');
      if (searchResults.length === 0) {
        console.error('Knowledge search test failed - no results found');
        return false;
      }
      console.log(`✓ Knowledge search test passed - found ${searchResults.length} results`);

      // Test customer context
      const testContext = {
        customer_identifier: 'test-learning-system',
        customer_email: '<EMAIL>',
        topics_of_interest: ['cybersecurity', 'cloud'],
        communication_style: 'professional'
      };
      
      await learningService.updateCustomerContext(testContext);
      const retrievedContext = await learningService.getCustomerContext('test-learning-system');
      
      if (!retrievedContext) {
        console.error('Customer context test failed - could not retrieve context');
        return false;
      }
      console.log('✓ Customer context test passed');

      // Test session creation and message logging
      const testSessionId = 'test-session-' + Date.now();
      await learningService.createChatSession({
        session_id: testSessionId,
        customer_identifier: 'test-learning-system'
      });

      await learningService.logMessage({
        session_id: testSessionId,
        message_order: 1,
        role: 'user',
        content: 'Test message for learning system'
      });

      console.log('✓ Session and message logging test passed');

      // Test knowledge stats
      const stats = await knowledgeManagementService.getKnowledgeStats();
      if (stats.total_faq_entries === 0) {
        console.error('Knowledge stats test failed - no FAQ entries found');
        return false;
      }
      console.log(`✓ Knowledge stats test passed - ${stats.total_faq_entries} FAQ entries found`);

      console.log('All learning system tests passed successfully!');
      return true;
    } catch (error) {
      console.error('Learning system test failed:', error);
      return false;
    }
  }

  // Run complete migration and testing
  async runCompleteMigration(): Promise<void> {
    try {
      console.log('=== Starting Complete Migration Process ===');
      
      // Step 1: Migrate existing knowledge
      await this.migrateExistingKnowledge();
      
      // Step 2: Create sample conversations
      await this.createSampleConversations();
      
      // Step 3: Test the system
      const testsPassed = await this.testLearningSystem();
      
      if (testsPassed) {
        console.log('=== Migration completed successfully! ===');
        console.log('The intelligent learning system is now active and ready to learn from conversations.');
      } else {
        console.error('=== Migration completed with errors ===');
        console.error('Some tests failed. Please check the logs and fix any issues.');
      }
    } catch (error) {
      console.error('Migration process failed:', error);
      throw error;
    }
  }

  // Validate system integrity
  async validateSystemIntegrity(): Promise<boolean> {
    try {
      console.log('Validating system integrity...');
      
      // Check database tables exist and have data
      const stats = await knowledgeManagementService.getKnowledgeStats();
      
      if (stats.total_faq_entries === 0 && stats.total_knowledge_entries === 0) {
        console.error('No knowledge entries found in database');
        return false;
      }

      console.log('✓ System integrity validation passed');
      return true;
    } catch (error) {
      console.error('System integrity validation failed:', error);
      return false;
    }
  }
}

export default new MigrationService();
