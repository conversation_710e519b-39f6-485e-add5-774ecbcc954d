import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// PostgreSQL connection configuration
const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'postgres',
  password: process.env.POSTGRES_PASSWORD || '',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
});

export interface ChatSession {
  id?: number;
  session_id: string;
  customer_identifier?: string;
  customer_name?: string;
  customer_email?: string;
  start_time?: Date;
  end_time?: Date;
  total_messages?: number;
  contact_collected?: boolean;
  satisfaction_rating?: number;
  conversation_outcome?: string;
}

export interface ConversationMessage {
  id?: number;
  session_id: string;
  message_order: number;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
  response_time_ms?: number;
  tokens_used?: number;
  model_used?: string;
  confidence_score?: number;
  intent_detected?: string;
  entities_extracted?: any;
  sentiment?: string;
}

export interface KnowledgeEntry {
  id?: number;
  category: string;
  question_pattern: string;
  response_template: string;
  keywords?: string[];
  context_tags?: string[];
  usage_count?: number;
  success_rate?: number;
  effectiveness_score?: number;
  source?: string;
  is_active?: boolean;
}

export interface CustomerContext {
  id?: number;
  customer_identifier: string;
  customer_email?: string;
  customer_name?: string;
  preferences?: any;
  interaction_history?: any;
  topics_of_interest?: string[];
  communication_style?: string;
  preferred_response_length?: string;
  last_interaction?: Date;
  total_interactions?: number;
  satisfaction_average?: number;
  notes?: string;
}

class LearningService {
  // Session Management
  async createChatSession(sessionData: ChatSession): Promise<string> {
    try {
      const query = `
        INSERT INTO chat_sessions
        (session_id, customer_identifier, customer_name, customer_email, conversation_outcome)
        VALUES ($1, $2, $3, $4, $5)
        ON CONFLICT (session_id) DO UPDATE SET
          customer_identifier = COALESCE(EXCLUDED.customer_identifier, chat_sessions.customer_identifier),
          customer_name = COALESCE(EXCLUDED.customer_name, chat_sessions.customer_name),
          customer_email = COALESCE(EXCLUDED.customer_email, chat_sessions.customer_email),
          updated_at = CURRENT_TIMESTAMP
        RETURNING session_id
      `;

      const values = [
        sessionData.session_id,
        sessionData.customer_identifier || null,
        sessionData.customer_name || null,
        sessionData.customer_email || null,
        sessionData.conversation_outcome || 'ongoing'
      ];

      const result = await pool.query(query, values);
      return result.rows[0].session_id;
    } catch (error) {
      console.error('Error creating chat session:', error);
      throw error;
    }
  }

  async sessionExists(sessionId: string): Promise<boolean> {
    try {
      const query = `SELECT 1 FROM chat_sessions WHERE session_id = $1 LIMIT 1`;
      const result = await pool.query(query, [sessionId]);
      return result.rows.length > 0;
    } catch (error) {
      console.error('Error checking session existence:', error);
      return false;
    }
  }

  async updateChatSession(sessionId: string, updates: Partial<ChatSession>): Promise<void> {
    try {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      Object.entries(updates).forEach(([key, value]) => {
        if (value !== undefined && key !== 'session_id') {
          setClause.push(`${key} = $${paramIndex}`);
          values.push(value);
          paramIndex++;
        }
      });

      if (setClause.length === 0) return;

      setClause.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(sessionId);

      const query = `
        UPDATE chat_sessions 
        SET ${setClause.join(', ')}
        WHERE session_id = $${paramIndex}
      `;

      await pool.query(query, values);
    } catch (error) {
      console.error('Error updating chat session:', error);
      throw error;
    }
  }

  // Message Logging
  async logMessage(messageData: ConversationMessage): Promise<number> {
    try {
      const query = `
        INSERT INTO conversation_messages 
        (session_id, message_order, role, content, response_time_ms, tokens_used, 
         model_used, confidence_score, intent_detected, entities_extracted, sentiment)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING id
      `;
      
      const values = [
        messageData.session_id,
        messageData.message_order,
        messageData.role,
        messageData.content,
        messageData.response_time_ms || null,
        messageData.tokens_used || null,
        messageData.model_used || null,
        messageData.confidence_score || null,
        messageData.intent_detected || null,
        messageData.entities_extracted ? JSON.stringify(messageData.entities_extracted) : null,
        messageData.sentiment || null
      ];

      const result = await pool.query(query, values);
      
      // Update session message count
      await this.incrementSessionMessageCount(messageData.session_id);
      
      return result.rows[0].id;
    } catch (error) {
      console.error('Error logging message:', error);
      throw error;
    }
  }

  private async incrementSessionMessageCount(sessionId: string): Promise<void> {
    try {
      const query = `
        UPDATE chat_sessions 
        SET total_messages = total_messages + 1, updated_at = CURRENT_TIMESTAMP
        WHERE session_id = $1
      `;
      await pool.query(query, [sessionId]);
    } catch (error) {
      console.error('Error incrementing message count:', error);
    }
  }

  // Knowledge Base Management
  async addKnowledgeEntry(entry: KnowledgeEntry): Promise<number> {
    try {
      const query = `
        INSERT INTO knowledge_base 
        (category, question_pattern, response_template, keywords, context_tags, 
         effectiveness_score, source, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id
      `;
      
      const values = [
        entry.category,
        entry.question_pattern,
        entry.response_template,
        entry.keywords || [],
        entry.context_tags || [],
        entry.effectiveness_score || 0.5,
        entry.source || 'learned',
        entry.is_active !== false
      ];

      const result = await pool.query(query, values);
      return result.rows[0].id;
    } catch (error) {
      console.error('Error adding knowledge entry:', error);
      throw error;
    }
  }

  async searchKnowledge(query: string, category?: string, limit: number = 5): Promise<KnowledgeEntry[]> {
    try {
      let sql = `
        SELECT * FROM knowledge_base 
        WHERE is_active = true 
        AND (
          question_pattern ILIKE $1 
          OR $2 = ANY(keywords)
          OR response_template ILIKE $1
        )
      `;
      
      const values = [`%${query}%`, query];
      
      if (category) {
        sql += ` AND category = $3`;
        values.push(category);
        sql += ` ORDER BY effectiveness_score DESC, usage_count DESC LIMIT $4`;
        values.push(limit.toString());
      } else {
        sql += ` ORDER BY effectiveness_score DESC, usage_count DESC LIMIT $3`;
        values.push(limit.toString());
      }

      const result = await pool.query(sql, values);
      return result.rows;
    } catch (error) {
      console.error('Error searching knowledge:', error);
      return [];
    }
  }

  async updateKnowledgeUsage(knowledgeId: number, wasSuccessful: boolean): Promise<void> {
    try {
      const query = `
        UPDATE knowledge_base 
        SET usage_count = usage_count + 1,
            success_rate = CASE 
              WHEN usage_count = 0 THEN CASE WHEN $2 THEN 100.0 ELSE 0.0 END
              ELSE (success_rate * usage_count + CASE WHEN $2 THEN 100.0 ELSE 0.0 END) / (usage_count + 1)
            END,
            last_used = CURRENT_TIMESTAMP,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = $1
      `;
      
      await pool.query(query, [knowledgeId, wasSuccessful]);
    } catch (error) {
      console.error('Error updating knowledge usage:', error);
    }
  }

  // Customer Context Management
  async getCustomerContext(customerIdentifier: string): Promise<CustomerContext | null> {
    try {
      const query = `
        SELECT * FROM customer_context 
        WHERE customer_identifier = $1 OR customer_email = $1
        ORDER BY last_interaction DESC
        LIMIT 1
      `;
      
      const result = await pool.query(query, [customerIdentifier]);
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error getting customer context:', error);
      return null;
    }
  }

  async updateCustomerContext(context: CustomerContext): Promise<void> {
    try {
      const existingContext = await this.getCustomerContext(context.customer_identifier);
      
      if (existingContext) {
        const query = `
          UPDATE customer_context 
          SET customer_email = COALESCE($2, customer_email),
              customer_name = COALESCE($3, customer_name),
              preferences = COALESCE($4, preferences),
              interaction_history = $5,
              topics_of_interest = COALESCE($6, topics_of_interest),
              communication_style = COALESCE($7, communication_style),
              preferred_response_length = COALESCE($8, preferred_response_length),
              last_interaction = CURRENT_TIMESTAMP,
              total_interactions = total_interactions + 1,
              satisfaction_average = COALESCE($9, satisfaction_average),
              notes = COALESCE($10, notes),
              updated_at = CURRENT_TIMESTAMP
          WHERE customer_identifier = $1
        `;
        
        const values = [
          context.customer_identifier,
          context.customer_email,
          context.customer_name,
          context.preferences ? JSON.stringify(context.preferences) : null,
          context.interaction_history ? JSON.stringify(context.interaction_history) : null,
          context.topics_of_interest,
          context.communication_style,
          context.preferred_response_length,
          context.satisfaction_average,
          context.notes
        ];
        
        await pool.query(query, values);
      } else {
        const query = `
          INSERT INTO customer_context 
          (customer_identifier, customer_email, customer_name, preferences, 
           interaction_history, topics_of_interest, communication_style, 
           preferred_response_length, total_interactions, satisfaction_average, notes)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 1, $9, $10)
        `;
        
        const values = [
          context.customer_identifier,
          context.customer_email,
          context.customer_name,
          context.preferences ? JSON.stringify(context.preferences) : null,
          context.interaction_history ? JSON.stringify(context.interaction_history) : null,
          context.topics_of_interest,
          context.communication_style,
          context.preferred_response_length,
          context.satisfaction_average,
          context.notes
        ];
        
        await pool.query(query, values);
      }
    } catch (error) {
      console.error('Error updating customer context:', error);
      throw error;
    }
  }
}

export default new LearningService();
