import { Pool } from 'pg';
import dotenv from 'dotenv';

dotenv.config();

// PostgreSQL connection configuration
const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'postgres',
  password: process.env.POSTGRES_PASSWORD || '',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
});

export interface FAQEntry {
  id?: number;
  question: string;
  answer: string;
  category: string;
  keywords: string[];
  variations: string[];
  usage_count?: number;
  success_rate?: number;
  confidence_score?: number;
  is_active?: boolean;
}

export interface KnowledgeSearchResult {
  id: number;
  question_pattern: string;
  response_template: string;
  category: string;
  keywords: string[];
  effectiveness_score: number;
  usage_count: number;
  relevance_score: number;
}

class KnowledgeManagementService {
  // Initialize with default knowledge base
  async initializeDefaultKnowledge(): Promise<void> {
    try {
      console.log('Initializing default knowledge base...');
      
      const defaultFAQs: FAQEntry[] = [
        {
          question: "What services does SpireLab offer?",
          answer: "SpireLab offers comprehensive IT solutions including web development, mobile app development, cloud solutions, AI/ML services, cybersecurity, and digital transformation consulting. We specialize in creating custom software solutions for businesses of all sizes.",
          category: "services",
          keywords: ["services", "what", "offer", "do", "spirelab"],
          variations: [
            "What do you do?",
            "What kind of services do you provide?",
            "Tell me about your services",
            "What does your company do?"
          ],
          confidence_score: 1.0
        },
        {
          question: "How can I contact SpireLab?",
          answer: "You can contact SpireLab through multiple channels: Email <NAME_EMAIL>, call us at ******-SPIRE-LAB, or fill out our contact form on the website. We typically respond within 24 hours during business days.",
          category: "contact",
          keywords: ["contact", "reach", "phone", "email", "how"],
          variations: [
            "How do I reach you?",
            "What's your contact information?",
            "How can I get in touch?",
            "Contact details"
          ],
          confidence_score: 1.0
        },
        {
          question: "What are your pricing models?",
          answer: "Our pricing varies based on project scope, complexity, and timeline. We offer flexible pricing models including fixed-price projects, hourly rates, and retainer agreements. Contact us for a free consultation and customized quote based on your specific needs.",
          category: "pricing",
          keywords: ["price", "cost", "pricing", "how much", "rates"],
          variations: [
            "How much do you charge?",
            "What does it cost?",
            "Pricing information",
            "How much for a project?"
          ],
          confidence_score: 1.0
        },
        {
          question: "How long does a typical project take?",
          answer: "Project timelines vary depending on complexity and scope. Simple websites typically take 2-4 weeks, while complex applications can take 3-6 months. We provide detailed project timelines during our initial consultation and keep you updated throughout the development process.",
          category: "timeline",
          keywords: ["time", "long", "timeline", "duration", "when"],
          variations: [
            "How long will it take?",
            "What's the timeline?",
            "When will it be done?",
            "Project duration"
          ],
          confidence_score: 1.0
        },
        {
          question: "Do you provide ongoing support and maintenance?",
          answer: "Yes, we offer comprehensive support and maintenance packages. This includes regular updates, security patches, performance monitoring, and technical support. We have different support tiers to match your needs and budget.",
          category: "support",
          keywords: ["support", "maintenance", "ongoing", "after", "help"],
          variations: [
            "Do you provide support?",
            "What about maintenance?",
            "Support after launch?",
            "Ongoing help"
          ],
          confidence_score: 1.0
        }
      ];

      // Check if knowledge base is already initialized
      const existingCount = await this.getKnowledgeCount();
      if (existingCount > 0) {
        console.log(`Knowledge base already has ${existingCount} entries`);
        return;
      }

      // Insert default FAQs
      for (const faq of defaultFAQs) {
        await this.addFAQEntry(faq);
      }

      console.log(`Initialized knowledge base with ${defaultFAQs.length} default entries`);
    } catch (error) {
      console.error('Error initializing default knowledge:', error);
    }
  }

  // Add FAQ entry
  async addFAQEntry(faq: FAQEntry): Promise<number> {
    try {
      const query = `
        INSERT INTO faq_entries 
        (question, answer, category, keywords, variations, confidence_score, is_active)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING id
      `;
      
      const values = [
        faq.question,
        faq.answer,
        faq.category,
        faq.keywords || [],
        faq.variations || [],
        faq.confidence_score || 1.0,
        faq.is_active !== false
      ];

      const result = await pool.query(query, values);
      return result.rows[0].id;
    } catch (error) {
      console.error('Error adding FAQ entry:', error);
      throw error;
    }
  }

  // Search knowledge base with intelligent matching
  async searchKnowledge(query: string, category?: string, limit: number = 5): Promise<KnowledgeSearchResult[]> {
    try {
      const searchTerms = this.extractSearchTerms(query);
      
      let sql = `
        WITH knowledge_search AS (
          SELECT 
            kb.id,
            kb.question_pattern,
            kb.response_template,
            kb.category,
            kb.keywords,
            kb.effectiveness_score,
            kb.usage_count,
            -- Calculate relevance score
            (
              CASE WHEN kb.question_pattern ILIKE $1 THEN 10 ELSE 0 END +
              CASE WHEN EXISTS (
                SELECT 1 FROM unnest(kb.keywords) AS keyword 
                WHERE $2 ILIKE '%' || keyword || '%'
              ) THEN 5 ELSE 0 END +
              CASE WHEN kb.response_template ILIKE $1 THEN 3 ELSE 0 END +
              (kb.effectiveness_score * 2)
            ) AS relevance_score
          FROM knowledge_base kb
          WHERE kb.is_active = true
        ),
        faq_search AS (
          SELECT 
            faq.id + 10000 as id, -- Offset to avoid conflicts
            faq.question as question_pattern,
            faq.answer as response_template,
            faq.category,
            faq.keywords,
            (faq.success_rate / 100.0) as effectiveness_score,
            faq.usage_count,
            -- Calculate relevance score for FAQ
            (
              CASE WHEN faq.question ILIKE $1 THEN 15 ELSE 0 END +
              CASE WHEN EXISTS (
                SELECT 1 FROM unnest(faq.variations) AS variation 
                WHERE variation ILIKE $1
              ) THEN 12 ELSE 0 END +
              CASE WHEN EXISTS (
                SELECT 1 FROM unnest(faq.keywords) AS keyword 
                WHERE $2 ILIKE '%' || keyword || '%'
              ) THEN 8 ELSE 0 END +
              CASE WHEN faq.answer ILIKE $1 THEN 3 ELSE 0 END +
              (faq.confidence_score * 5)
            ) AS relevance_score
          FROM faq_entries faq
          WHERE faq.is_active = true
        )
        SELECT * FROM (
          SELECT * FROM knowledge_search
          UNION ALL
          SELECT * FROM faq_search
        ) combined
        WHERE relevance_score > 0
      `;
      
      const values = [`%${query}%`, query.toLowerCase()];
      
      if (category) {
        sql += ` AND category = $3`;
        values.push(category);
        sql += ` ORDER BY relevance_score DESC, effectiveness_score DESC LIMIT $4`;
        values.push(limit.toString());
      } else {
        sql += ` ORDER BY relevance_score DESC, effectiveness_score DESC LIMIT $3`;
        values.push(limit.toString());
      }

      const result = await pool.query(sql, values);
      return result.rows;
    } catch (error) {
      console.error('Error searching knowledge:', error);
      return [];
    }
  }

  // Get personalized response based on customer context
  async getPersonalizedResponse(query: string, customerContext?: any): Promise<KnowledgeSearchResult | null> {
    try {
      let searchResults = await this.searchKnowledge(query, undefined, 3);
      
      if (searchResults.length === 0) {
        return null;
      }

      // If we have customer context, personalize the response
      if (customerContext) {
        searchResults = this.personalizeResults(searchResults, customerContext);
      }

      return searchResults[0];
    } catch (error) {
      console.error('Error getting personalized response:', error);
      return null;
    }
  }

  // Update knowledge effectiveness based on usage
  async updateKnowledgeEffectiveness(knowledgeId: number, wasSuccessful: boolean, responseTime?: number): Promise<void> {
    try {
      // Determine if this is from knowledge_base or faq_entries
      const isFromFAQ = knowledgeId > 10000;
      const actualId = isFromFAQ ? knowledgeId - 10000 : knowledgeId;
      
      if (isFromFAQ) {
        const query = `
          UPDATE faq_entries 
          SET usage_count = usage_count + 1,
              success_rate = CASE 
                WHEN usage_count = 0 THEN CASE WHEN $2 THEN 100.0 ELSE 0.0 END
                ELSE (success_rate * usage_count + CASE WHEN $2 THEN 100.0 ELSE 0.0 END) / (usage_count + 1)
              END,
              last_updated = CURRENT_TIMESTAMP
          WHERE id = $1
        `;
        
        await pool.query(query, [actualId, wasSuccessful]);
      } else {
        const query = `
          UPDATE knowledge_base 
          SET usage_count = usage_count + 1,
              success_rate = CASE 
                WHEN usage_count = 0 THEN CASE WHEN $2 THEN 100.0 ELSE 0.0 END
                ELSE (success_rate * usage_count + CASE WHEN $2 THEN 100.0 ELSE 0.0 END) / (usage_count + 1)
              END,
              effectiveness_score = CASE 
                WHEN usage_count = 0 THEN CASE WHEN $2 THEN 0.8 ELSE 0.3 END
                ELSE LEAST(1.0, effectiveness_score + CASE WHEN $2 THEN 0.1 ELSE -0.05 END)
              END,
              last_used = CURRENT_TIMESTAMP,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = $1
        `;
        
        await pool.query(query, [actualId, wasSuccessful]);
      }
    } catch (error) {
      console.error('Error updating knowledge effectiveness:', error);
    }
  }

  // Get knowledge statistics
  async getKnowledgeStats(): Promise<any> {
    try {
      const queries = [
        'SELECT COUNT(*) as total_knowledge FROM knowledge_base WHERE is_active = true',
        'SELECT COUNT(*) as total_faqs FROM faq_entries WHERE is_active = true',
        'SELECT AVG(effectiveness_score) as avg_effectiveness FROM knowledge_base WHERE is_active = true',
        'SELECT AVG(success_rate) as avg_success_rate FROM faq_entries WHERE is_active = true',
        'SELECT SUM(usage_count) as total_usage FROM knowledge_base',
        'SELECT SUM(usage_count) as total_faq_usage FROM faq_entries'
      ];

      const results = await Promise.all(queries.map(query => pool.query(query)));
      
      return {
        total_knowledge_entries: parseInt(results[0].rows[0].total_knowledge),
        total_faq_entries: parseInt(results[1].rows[0].total_faqs),
        average_effectiveness: parseFloat(results[2].rows[0].avg_effectiveness) || 0,
        average_success_rate: parseFloat(results[3].rows[0].avg_success_rate) || 0,
        total_knowledge_usage: parseInt(results[4].rows[0].total_usage) || 0,
        total_faq_usage: parseInt(results[5].rows[0].total_faq_usage) || 0
      };
    } catch (error) {
      console.error('Error getting knowledge stats:', error);
      return {};
    }
  }

  // Helper methods
  private async getKnowledgeCount(): Promise<number> {
    try {
      const result = await pool.query('SELECT COUNT(*) as count FROM faq_entries');
      return parseInt(result.rows[0].count);
    } catch (error) {
      console.error('Error getting knowledge count:', error);
      return 0;
    }
  }

  private extractSearchTerms(query: string): string[] {
    return query.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(term => term.length > 2)
      .filter(term => !['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(term));
  }

  private personalizeResults(results: KnowledgeSearchResult[], customerContext: any): KnowledgeSearchResult[] {
    // Adjust relevance based on customer context
    return results.map(result => {
      let adjustedScore = result.relevance_score;
      
      // Boost score if category matches customer interests
      if (customerContext.topics_of_interest?.includes(result.category)) {
        adjustedScore += 5;
      }
      
      // Adjust based on communication style
      if (customerContext.communication_style === 'formal' && result.response_template.includes('Hi there')) {
        adjustedScore -= 2;
      } else if (customerContext.communication_style === 'casual' && result.response_template.includes('Dear')) {
        adjustedScore -= 2;
      }
      
      // Adjust based on preferred response length
      const responseLength = result.response_template.length;
      if (customerContext.preferred_response_length === 'short' && responseLength > 200) {
        adjustedScore -= 3;
      } else if (customerContext.preferred_response_length === 'detailed' && responseLength < 100) {
        adjustedScore -= 2;
      }
      
      return {
        ...result,
        relevance_score: adjustedScore
      };
    }).sort((a, b) => b.relevance_score - a.relevance_score);
  }

  // Batch operations for maintenance
  async cleanupInactiveKnowledge(): Promise<void> {
    try {
      // Deactivate knowledge entries with very low effectiveness
      await pool.query(`
        UPDATE knowledge_base 
        SET is_active = false 
        WHERE effectiveness_score < 0.2 AND usage_count > 10
      `);
      
      await pool.query(`
        UPDATE faq_entries 
        SET is_active = false 
        WHERE success_rate < 20.0 AND usage_count > 10
      `);
      
      console.log('Cleaned up inactive knowledge entries');
    } catch (error) {
      console.error('Error cleaning up knowledge:', error);
    }
  }

  async optimizeKnowledgeBase(): Promise<void> {
    try {
      // Remove duplicate entries
      await pool.query(`
        DELETE FROM knowledge_base kb1 
        WHERE EXISTS (
          SELECT 1 FROM knowledge_base kb2 
          WHERE kb2.id > kb1.id 
          AND kb2.question_pattern = kb1.question_pattern
          AND kb2.category = kb1.category
        )
      `);
      
      console.log('Optimized knowledge base');
    } catch (error) {
      console.error('Error optimizing knowledge base:', error);
    }
  }
}

export default new KnowledgeManagementService();
