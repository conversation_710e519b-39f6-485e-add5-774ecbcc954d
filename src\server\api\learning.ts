import express from 'express';
import learningService, { ChatSession, ConversationMessage, KnowledgeEntry, CustomerContext } from '../../services/learningService';
import backgroundLearningService from '../../services/backgroundLearningService';
import knowledgeManagementService from '../../services/knowledgeManagementService';
import effectivenessTrackingService from '../../services/effectivenessTrackingService';
import patternRecognitionService from '../../services/patternRecognitionService';

const router = express.Router();

// Session Management Endpoints
router.post('/sessions', async (req, res) => {
  try {
    const sessionData: ChatSession = req.body;
    const sessionId = await learningService.createChatSession(sessionData);
    
    res.status(201).json({
      success: true,
      session_id: sessionId,
      message: 'Chat session created successfully'
    });
  } catch (error) {
    console.error('Error creating chat session:', error);
    res.status(500).json({
      error: 'Failed to create chat session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.put('/sessions/:sessionId', async (req, res) => {
  try {
    const { sessionId } = req.params;
    const updates: Partial<ChatSession> = req.body;
    
    await learningService.updateChatSession(sessionId, updates);
    
    res.json({
      success: true,
      message: 'Chat session updated successfully'
    });
  } catch (error) {
    console.error('Error updating chat session:', error);
    res.status(500).json({
      error: 'Failed to update chat session',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Message Logging Endpoints
router.post('/messages', async (req, res) => {
  try {
    const messageData: ConversationMessage = req.body;
    const messageId = await learningService.logMessage(messageData);
    
    res.status(201).json({
      success: true,
      message_id: messageId,
      message: 'Message logged successfully'
    });
  } catch (error) {
    console.error('Error logging message:', error);
    res.status(500).json({
      error: 'Failed to log message',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Knowledge Base Endpoints
router.post('/knowledge', async (req, res) => {
  try {
    const entry: KnowledgeEntry = req.body;
    const entryId = await learningService.addKnowledgeEntry(entry);
    
    res.status(201).json({
      success: true,
      entry_id: entryId,
      message: 'Knowledge entry added successfully'
    });
  } catch (error) {
    console.error('Error adding knowledge entry:', error);
    res.status(500).json({
      error: 'Failed to add knowledge entry',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/knowledge/search', async (req, res) => {
  try {
    const { query, category, limit } = req.query;
    
    if (!query || typeof query !== 'string') {
      return res.status(400).json({
        error: 'Query parameter is required and must be a string'
      });
    }
    
    const results = await learningService.searchKnowledge(
      query,
      category as string,
      limit ? parseInt(limit as string) : 5
    );
    
    res.json({
      success: true,
      results,
      count: results.length
    });
  } catch (error) {
    console.error('Error searching knowledge:', error);
    res.status(500).json({
      error: 'Failed to search knowledge base',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.put('/knowledge/:id/usage', async (req, res) => {
  try {
    const { id } = req.params;
    const { wasSuccessful } = req.body;
    
    if (typeof wasSuccessful !== 'boolean') {
      return res.status(400).json({
        error: 'wasSuccessful parameter is required and must be a boolean'
      });
    }
    
    await learningService.updateKnowledgeUsage(parseInt(id), wasSuccessful);
    
    res.json({
      success: true,
      message: 'Knowledge usage updated successfully'
    });
  } catch (error) {
    console.error('Error updating knowledge usage:', error);
    res.status(500).json({
      error: 'Failed to update knowledge usage',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Customer Context Endpoints
router.get('/context/:identifier', async (req, res) => {
  try {
    const { identifier } = req.params;
    const context = await learningService.getCustomerContext(identifier);
    
    if (!context) {
      return res.status(404).json({
        error: 'Customer context not found'
      });
    }
    
    res.json({
      success: true,
      context
    });
  } catch (error) {
    console.error('Error getting customer context:', error);
    res.status(500).json({
      error: 'Failed to get customer context',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/context', async (req, res) => {
  try {
    const contextData: CustomerContext = req.body;
    
    if (!contextData.customer_identifier) {
      return res.status(400).json({
        error: 'customer_identifier is required'
      });
    }
    
    await learningService.updateCustomerContext(contextData);
    
    res.json({
      success: true,
      message: 'Customer context updated successfully'
    });
  } catch (error) {
    console.error('Error updating customer context:', error);
    res.status(500).json({
      error: 'Failed to update customer context',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Analytics Endpoints
router.get('/analytics/sessions', async (req, res) => {
  try {
    const { days = 7 } = req.query;
    const metrics = await effectivenessTrackingService.getEffectivenessMetrics(parseInt(days as string));

    res.json({
      success: true,
      metrics,
      period_days: parseInt(days as string)
    });
  } catch (error) {
    console.error('Error getting session analytics:', error);
    res.status(500).json({
      error: 'Failed to get session analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/analytics/knowledge', async (req, res) => {
  try {
    const stats = await knowledgeManagementService.getKnowledgeStats();
    const { days = 7 } = req.query;
    const effectivenessByType = await effectivenessTrackingService.getEffectivenessByType(parseInt(days as string));

    res.json({
      success: true,
      knowledge_stats: stats,
      effectiveness_by_type: effectivenessByType,
      period_days: parseInt(days as string)
    });
  } catch (error) {
    console.error('Error getting knowledge analytics:', error);
    res.status(500).json({
      error: 'Failed to get knowledge analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// System Management Endpoints
router.get('/system/status', async (req, res) => {
  try {
    const status = backgroundLearningService.getStatus();
    const knowledgeStats = await knowledgeManagementService.getKnowledgeStats();
    const effectivenessMetrics = await effectivenessTrackingService.getEffectivenessMetrics(7);

    res.json({
      success: true,
      system_status: status,
      knowledge_stats: knowledgeStats,
      effectiveness_metrics: effectivenessMetrics
    });
  } catch (error) {
    console.error('Error getting system status:', error);
    res.status(500).json({
      error: 'Failed to get system status',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.post('/system/trigger-learning', async (req, res) => {
  try {
    await backgroundLearningService.triggerImmediateLearning();

    res.json({
      success: true,
      message: 'Learning process triggered successfully'
    });
  } catch (error) {
    console.error('Error triggering learning:', error);
    res.status(500).json({
      error: 'Failed to trigger learning process',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

router.get('/analytics/top-responses', async (req, res) => {
  try {
    const { limit = 10 } = req.query;
    const topResponses = await effectivenessTrackingService.getTopPerformingResponses(parseInt(limit as string));

    res.json({
      success: true,
      top_responses: topResponses,
      count: topResponses.length
    });
  } catch (error) {
    console.error('Error getting top responses:', error);
    res.status(500).json({
      error: 'Failed to get top performing responses',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export default router;
