import { Pool } from 'pg';
import dotenv from 'dotenv';
import learningService from './learningService';

dotenv.config();

// PostgreSQL connection configuration
const pool = new Pool({
  user: process.env.POSTGRES_USER || 'postgres',
  host: process.env.POSTGRES_HOST || 'localhost',
  database: process.env.POSTGRES_DB || 'postgres',
  password: process.env.POSTGRES_PASSWORD || '',
  port: parseInt(process.env.POSTGRES_PORT || '5433'),
});

interface QuestionPattern {
  pattern: string;
  frequency: number;
  category: string;
  keywords: string[];
  variations: string[];
}

interface ResponsePattern {
  trigger: string;
  response: string;
  success_rate: number;
  context: any;
}

interface ConversationFlow {
  flow_type: string;
  steps: string[];
  success_rate: number;
  outcomes: any;
}

class PatternRecognitionService {
  // Extract common question patterns from conversations
  async extractQuestionPatterns(limit: number = 100): Promise<QuestionPattern[]> {
    try {
      const query = `
        SELECT 
          content,
          COUNT(*) as frequency,
          ARRAY_AGG(DISTINCT intent_detected) FILTER (WHERE intent_detected IS NOT NULL) as intents,
          ARRAY_AGG(DISTINCT entities_extracted::text) FILTER (WHERE entities_extracted IS NOT NULL) as entities
        FROM conversation_messages 
        WHERE role = 'user' 
          AND created_at >= NOW() - INTERVAL '30 days'
          AND LENGTH(content) > 10
        GROUP BY content
        HAVING COUNT(*) >= 2
        ORDER BY frequency DESC
        LIMIT $1
      `;

      const result = await pool.query(query, [limit]);
      
      const patterns: QuestionPattern[] = [];
      
      for (const row of result.rows) {
        const keywords = this.extractKeywords(row.content);
        const category = this.categorizeQuestion(row.content, keywords);
        const variations = await this.findSimilarQuestions(row.content);
        
        patterns.push({
          pattern: row.content,
          frequency: parseInt(row.frequency),
          category,
          keywords,
          variations
        });
      }

      return patterns;
    } catch (error) {
      console.error('Error extracting question patterns:', error);
      return [];
    }
  }

  // Extract successful response patterns
  async extractResponsePatterns(limit: number = 50): Promise<ResponsePattern[]> {
    try {
      const query = `
        SELECT 
          cm.content as response,
          prev_cm.content as trigger,
          COUNT(*) as usage_count,
          AVG(CASE WHEN re.conversation_continued THEN 1.0 ELSE 0.0 END) as success_rate,
          AVG(CASE WHEN re.led_to_contact_collection THEN 1.0 ELSE 0.0 END) as contact_rate
        FROM conversation_messages cm
        JOIN conversation_messages prev_cm ON cm.session_id = prev_cm.session_id 
          AND cm.message_order = prev_cm.message_order + 1
        LEFT JOIN response_effectiveness re ON cm.id = re.message_id
        WHERE cm.role = 'assistant' 
          AND prev_cm.role = 'user'
          AND cm.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY cm.content, prev_cm.content
        HAVING COUNT(*) >= 2 AND AVG(CASE WHEN re.conversation_continued THEN 1.0 ELSE 0.0 END) > 0.7
        ORDER BY success_rate DESC, usage_count DESC
        LIMIT $1
      `;

      const result = await pool.query(query, [limit]);
      
      return result.rows.map(row => ({
        trigger: row.trigger,
        response: row.response,
        success_rate: parseFloat(row.success_rate) || 0,
        context: {
          usage_count: parseInt(row.usage_count),
          contact_rate: parseFloat(row.contact_rate) || 0
        }
      }));
    } catch (error) {
      console.error('Error extracting response patterns:', error);
      return [];
    }
  }

  // Analyze conversation flows that lead to successful outcomes
  async analyzeSuccessfulFlows(): Promise<ConversationFlow[]> {
    try {
      const query = `
        SELECT 
          cs.session_id,
          cs.conversation_outcome,
          cs.contact_collected,
          ARRAY_AGG(cm.content ORDER BY cm.message_order) as conversation_flow
        FROM chat_sessions cs
        JOIN conversation_messages cm ON cs.session_id = cm.session_id
        WHERE cs.conversation_outcome IN ('completed', 'contact_collected')
          AND cs.created_at >= NOW() - INTERVAL '30 days'
        GROUP BY cs.session_id, cs.conversation_outcome, cs.contact_collected
        HAVING COUNT(cm.id) >= 4 AND COUNT(cm.id) <= 20
      `;

      const result = await pool.query(query);
      
      const flows: ConversationFlow[] = [];
      const flowPatterns = new Map<string, { count: number; successful: number; outcomes: any[] }>();

      for (const row of result.rows) {
        const flowKey = this.generateFlowPattern(row.conversation_flow);
        
        if (!flowPatterns.has(flowKey)) {
          flowPatterns.set(flowKey, { count: 0, successful: 0, outcomes: [] });
        }
        
        const pattern = flowPatterns.get(flowKey)!;
        pattern.count++;
        
        if (row.conversation_outcome === 'contact_collected' || row.contact_collected) {
          pattern.successful++;
        }
        
        pattern.outcomes.push({
          outcome: row.conversation_outcome,
          contact_collected: row.contact_collected
        });
      }

      // Convert to ConversationFlow objects
      for (const [flowKey, data] of flowPatterns.entries()) {
        if (data.count >= 2) { // Only include patterns that occurred multiple times
          flows.push({
            flow_type: this.classifyFlowType(flowKey),
            steps: flowKey.split(' -> '),
            success_rate: data.successful / data.count,
            outcomes: data.outcomes
          });
        }
      }

      return flows.sort((a, b) => b.success_rate - a.success_rate);
    } catch (error) {
      console.error('Error analyzing successful flows:', error);
      return [];
    }
  }

  // Process and learn from recent conversations
  async processRecentConversations(): Promise<void> {
    try {
      console.log('Starting pattern recognition processing...');
      
      // Extract question patterns
      const questionPatterns = await this.extractQuestionPatterns(50);
      console.log(`Found ${questionPatterns.length} question patterns`);
      
      // Extract response patterns
      const responsePatterns = await this.extractResponsePatterns(30);
      console.log(`Found ${responsePatterns.length} response patterns`);
      
      // Analyze successful flows
      const successfulFlows = await this.analyzeSuccessfulFlows();
      console.log(`Found ${successfulFlows.length} successful conversation flows`);
      
      // Store learned patterns in the knowledge base
      for (const pattern of questionPatterns) {
        if (pattern.frequency >= 3) { // Only store frequently asked questions
          const responsePattern = responsePatterns.find(rp => 
            this.isRelated(rp.trigger, pattern.pattern)
          );
          
          if (responsePattern) {
            await learningService.addKnowledgeEntry({
              category: pattern.category,
              question_pattern: pattern.pattern,
              response_template: responsePattern.response,
              keywords: pattern.keywords,
              context_tags: [pattern.category, 'auto_learned'],
              effectiveness_score: responsePattern.success_rate,
              source: 'pattern_recognition'
            });
          }
        }
      }
      
      // Store learned patterns in the learned_patterns table
      for (const flow of successfulFlows) {
        if (flow.success_rate > 0.6) {
          await this.storeLearnedPattern({
            pattern_type: 'conversation_flow',
            pattern_data: {
              flow_type: flow.flow_type,
              steps: flow.steps,
              success_indicators: flow.outcomes
            },
            frequency: flow.outcomes.length,
            success_rate: flow.success_rate * 100,
            confidence_level: Math.min(flow.success_rate, 0.95)
          });
        }
      }
      
      console.log('Pattern recognition processing completed');
    } catch (error) {
      console.error('Error processing recent conversations:', error);
    }
  }

  // Helper methods
  private extractKeywords(text: string): string[] {
    // Simple keyword extraction - in production, you might use NLP libraries
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !['what', 'when', 'where', 'how', 'why', 'can', 'could', 'would', 'should'].includes(word));
    
    // Return unique words
    return [...new Set(words)];
  }

  private categorizeQuestion(question: string, keywords: string[]): string {
    const lowerQuestion = question.toLowerCase();
    
    if (lowerQuestion.includes('price') || lowerQuestion.includes('cost') || lowerQuestion.includes('pricing')) {
      return 'pricing';
    } else if (lowerQuestion.includes('service') || lowerQuestion.includes('what do you do')) {
      return 'services';
    } else if (lowerQuestion.includes('contact') || lowerQuestion.includes('reach') || lowerQuestion.includes('phone')) {
      return 'contact';
    } else if (lowerQuestion.includes('time') || lowerQuestion.includes('when') || lowerQuestion.includes('schedule')) {
      return 'scheduling';
    } else if (lowerQuestion.includes('how') && (lowerQuestion.includes('work') || lowerQuestion.includes('process'))) {
      return 'process';
    } else {
      return 'general';
    }
  }

  private async findSimilarQuestions(question: string, limit: number = 5): Promise<string[]> {
    try {
      // Simple similarity based on shared keywords - in production, use semantic similarity
      const keywords = this.extractKeywords(question);
      if (keywords.length === 0) return [];
      
      const query = `
        SELECT DISTINCT content
        FROM conversation_messages 
        WHERE role = 'user' 
          AND content != $1
          AND (${keywords.map((_, i) => `content ILIKE $${i + 2}`).join(' OR ')})
        LIMIT $${keywords.length + 2}
      `;
      
      const values = [question, ...keywords.map(k => `%${k}%`), limit];
      const result = await pool.query(query, values);
      
      return result.rows.map(row => row.content);
    } catch (error) {
      console.error('Error finding similar questions:', error);
      return [];
    }
  }

  private generateFlowPattern(conversationFlow: string[]): string {
    // Simplify conversation flow to key patterns
    const simplified = conversationFlow.map(message => {
      const lower = message.toLowerCase();
      if (lower.includes('hello') || lower.includes('hi')) return 'greeting';
      if (lower.includes('price') || lower.includes('cost')) return 'pricing_inquiry';
      if (lower.includes('service')) return 'service_inquiry';
      if (lower.includes('contact') || lower.includes('email')) return 'contact_request';
      if (lower.includes('thank')) return 'thanks';
      return 'general_question';
    });
    
    return simplified.join(' -> ');
  }

  private classifyFlowType(flowPattern: string): string {
    if (flowPattern.includes('contact_request')) return 'lead_generation';
    if (flowPattern.includes('pricing_inquiry')) return 'sales_inquiry';
    if (flowPattern.includes('service_inquiry')) return 'information_seeking';
    return 'general_conversation';
  }

  private isRelated(text1: string, text2: string): boolean {
    const keywords1 = new Set(this.extractKeywords(text1));
    const keywords2 = new Set(this.extractKeywords(text2));
    
    const intersection = new Set([...keywords1].filter(x => keywords2.has(x)));
    const union = new Set([...keywords1, ...keywords2]);
    
    // Jaccard similarity
    return intersection.size / union.size > 0.3;
  }

  private async storeLearnedPattern(pattern: any): Promise<void> {
    try {
      const query = `
        INSERT INTO learned_patterns 
        (pattern_type, pattern_data, frequency, success_rate, confidence_level, is_validated)
        VALUES ($1, $2, $3, $4, $5, $6)
        ON CONFLICT DO NOTHING
      `;
      
      const values = [
        pattern.pattern_type,
        JSON.stringify(pattern.pattern_data),
        pattern.frequency,
        pattern.success_rate,
        pattern.confidence_level,
        false // Will be validated later
      ];
      
      await pool.query(query, values);
    } catch (error) {
      console.error('Error storing learned pattern:', error);
    }
  }
}

export default new PatternRecognitionService();
