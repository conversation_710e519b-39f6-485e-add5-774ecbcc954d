import express from 'express';
import { OpenAI } from 'openai';
import dotenv from 'dotenv';
import { ChatCompletionMessageParam } from 'openai/resources';
import learningService from '../../services/learningService';
import knowledgeManagementService from '../../services/knowledgeManagementService';
import { v4 as uuidv4 } from 'uuid';

dotenv.config();

// Create router
const router = express.Router();

// Initialize OpenAI client with Nebius configuration
const openai = new OpenAI({
  baseURL: process.env.NEBIUS_BASE_URL || 'https://api.studio.nebius.com/v1/',
  apiKey: process.env.NEBIUS_API_KEY || '',
});

// Service information for the chatbot
const serviceInfo = `
  SpireLab offers comprehensive IT solutions including:
  
  **Remote IT Support**: 24/7 helpdesk services, remote troubleshooting, and system maintenance
  **Cybersecurity**: Threat protection, security audits, and incident response
  **Cloud Infrastructure**: Scalable solutions, migration services, and cloud optimization
  **Custom Software Development**: Tailored applications, web development, and mobile apps
  **Network Solutions**: Network design, implementation, and management
  **Managed IT Services**: Proactive monitoring, maintenance, and support
  **DevOps & CI/CD**: Automation, continuous integration, and deployment pipelines
  **Data Analytics**: Business intelligence, data visualization, and predictive analytics
  **Digital Transformation**: Process automation and digital strategy consulting
  **Ecommerce Solutions**: Online store development, payment integration, and optimization
  **Firewall Solutions**: Professional implementations for Sophos, FortiGate, Netgate, and SonicWall
`;

// System message to prevent thinking text
const systemMessage: ChatCompletionMessageParam = {
  role: 'system',
  content: `You are a helpful IT solutions assistant for SpireLab. Be concise, professional, and helpful.
  
  CRITICAL INSTRUCTIONS - NEVER VIOLATE THESE:
  - NEVER EVER include your thinking process, reasoning, or internal thoughts in your response
  - NEVER EVER use <think>, <thinking>, or any similar tags anywhere in your response
  - NEVER EVER include phrases like "let me think", "I'm thinking", "thinking:", "let me check", "I need to", "I should", "let me structure", "make sure", "double-check", "alright", "okay", "wait", "maybe" at the start of sentences
  - NEVER EVER show your decision-making process or analysis
  - NEVER EVER include any meta-commentary about your response
  - NEVER EVER include reasoning like "The user is asking about..." or "I should present..." or "They have..." or "They might..."
  - NEVER EVER mention "frameworks they use" or "previous examples" or internal considerations
  - ALWAYS respond directly with the final answer only
  - ALWAYS be concise and to the point
  - ALWAYS start your response immediately with the actual answer
  - If listing services, start directly with "SpireLab provides..." or similar
  - RESPOND AS IF YOU ARE SPEAKING DIRECTLY TO THE CLIENT
  
  Provide accurate information about our services. Here's information about our services: ${serviceInfo}`
};

// Function to clean the response text
function cleanResponseText(text: string): string {
  if (!text) return '';
  
  console.log('Raw response:', text); // Debug log
  
  // First, remove any <think> or <thinking> tags and their content - be more aggressive
  let cleanedText = text.replace(/<think>[\s\S]*?<\/think>/gi, '');
  cleanedText = cleanedText.replace(/<thinking>[\s\S]*?<\/thinking>/gi, '');
  cleanedText = cleanedText.replace(/&lt;think&gt;[\s\S]*?&lt;\/think&gt;/gi, '');
  cleanedText = cleanedText.replace(/&lt;thinking&gt;[\s\S]*?&lt;\/thinking&gt;/gi, '');
  
  // Remove any content that starts with <think> even without closing tag
  cleanedText = cleanedText.replace(/<think>[\s\S]*/gi, '');
  cleanedText = cleanedText.replace(/&lt;think&gt;[\s\S]*/gi, '');
  
  // Split by sentences and filter out thinking content more aggressively
  const sentences = cleanedText.split(/[.!?]+/);
  const filteredSentences = sentences.filter(sentence => {
    const lowerSentence = sentence.toLowerCase().trim();
    return !(
      lowerSentence.includes('thinking') ||
      lowerSentence.includes('let me think') ||
      lowerSentence.includes('let me check') ||
      lowerSentence.includes('i need to') ||
      lowerSentence.includes('i should') ||
      lowerSentence.includes('make sure') ||
      lowerSentence.includes('double-check') ||
      lowerSentence.includes('alright') ||
      lowerSentence.includes('okay') ||
      lowerSentence.includes('wait') ||
      lowerSentence.includes('maybe') ||
      lowerSentence.includes('should i') ||
      lowerSentence.includes('don\'t forget') ||
      lowerSentence.includes('also mention') ||
      lowerSentence.includes('keep it') ||
      lowerSentence.includes('stick to') ||
      lowerSentence.includes('that should cover') ||
      sentence.trim().length < 10 // Remove very short fragments
    );
  });
  
  cleanedText = filteredSentences.join('. ').trim();
  
  // Remove any lines that contain thinking patterns
  cleanedText = cleanedText
    .split('\n')
    .filter(line => {
      const lowerLine = line.toLowerCase().trim();
      return !(
        lowerLine.startsWith('thinking:') ||
        lowerLine.startsWith('let me think') ||
        lowerLine.startsWith('i\'m thinking') ||
        lowerLine.startsWith('thinking about') ||
        lowerLine.startsWith('think:') ||
        lowerLine.startsWith('<think>') ||
        lowerLine.includes('thinking process') ||
        lowerLine.includes('let me analyze') ||
        lowerLine.includes('my thinking') ||
        lowerLine.includes('let me check') ||
        lowerLine.includes('i need to') ||
        lowerLine.includes('i should') ||
        lowerLine.includes('let me structure') ||
        lowerLine.includes('make sure to') ||
        lowerLine.includes('double-check') ||
        lowerLine.includes('alright, that should') ||
        /^\s*thinking\b/.test(lowerLine) ||
        /^\s*\*.*thinking.*\*\s*$/.test(lowerLine) ||
        /^\s*-.*thinking.*-\s*$/.test(lowerLine)
      );
    })
    .join('\n');
  
  // Remove any remaining thinking patterns with more aggressive regex
  cleanedText = cleanedText
    .replace(/\[thinking[^\]]*\]/gi, '')
    .replace(/\(thinking[^)]*\)/gi, '')
    .replace(/\*thinking[^*]*\*/gi, '')
    .replace(/thinking[:.].*$/gim, '')
    .replace(/let me think.*$/gim, '')
    .replace(/i need to.*$/gim, '')
    .replace(/i should.*$/gim, '')
    .replace(/let me check.*$/gim, '')
    .replace(/make sure.*$/gim, '')
    .replace(/double-check.*$/gim, '')
    .replace(/alright,.*$/gim, '')
    .replace(/okay,.*$/gim, '')
    .replace(/\n\s*\n/g, '\n')
    .trim();

  // Remove markdown formatting for better TTS readability
  cleanedText = cleanedText
    // Remove bold formatting (**text** or __text__)
    .replace(/\*\*(.*?)\*\*/g, '$1')
    .replace(/__(.*?)__/g, '$1')
    // Remove italic formatting (*text* or _text_)
    .replace(/\*(.*?)\*/g, '$1')
    .replace(/_(.*?)_/g, '$1')
    // Remove other markdown elements that might interfere with TTS
    .replace(/`(.*?)`/g, '$1')  // Remove code backticks
    .replace(/#{1,6}\s*/g, '')  // Remove heading markers
    .replace(/^\s*[-*+]\s*/gm, '• ')  // Convert markdown lists to bullet points
    .replace(/^\s*\d+\.\s*/gm, '')  // Remove numbered list markers
    // Clean up extra whitespace
    .replace(/\n\s*\n/g, '\n')
    .trim();

  console.log('Cleaned response:', cleanedText); // Debug log
  return cleanedText;
}

// POST endpoint for chat messages
router.post('/message', async (req, res) => {
  const startTime = Date.now();

  try {
    const { messages, sessionId, customerIdentifier } = req.body;

    if (!Array.isArray(messages)) {
      return res.status(400).json({ error: 'Messages must be an array' });
    }

    // Generate session ID if not provided
    const currentSessionId = sessionId || uuidv4();

    // Get or create customer context
    let customerContext = null;
    if (customerIdentifier) {
      customerContext = await learningService.getCustomerContext(customerIdentifier);
    }

    // Check knowledge base for similar questions first
    const userMessage = messages[messages.length - 1];
    let knowledgeResponse = null;
    let usedKnowledgeId = null;

    if (userMessage && userMessage.role === 'user') {
      // Try to get personalized response from knowledge management service
      knowledgeResponse = await knowledgeManagementService.getPersonalizedResponse(
        userMessage.content,
        customerContext
      );

      if (knowledgeResponse && knowledgeResponse.relevance_score > 8) {
        usedKnowledgeId = knowledgeResponse.id;
      } else {
        knowledgeResponse = null;
      }
    }

    let finalResponse = '';
    let modelUsed = '';
    let tokensUsed = 0;

    if (knowledgeResponse) {
      // Use learned knowledge
      finalResponse = knowledgeResponse.response_template;
      modelUsed = 'knowledge_base';

      // Update knowledge usage in the knowledge management service
      await knowledgeManagementService.updateKnowledgeEffectiveness(usedKnowledgeId!, true, responseTime);
    } else {
      // Prepare messages for the API call, incorporating customer context
      let contextualSystemMessage = systemMessage;
      if (customerContext) {
        contextualSystemMessage = {
          ...systemMessage,
          content: systemMessage.content + `\n\nCustomer Context: ${customerContext.communication_style ? `Communication style: ${customerContext.communication_style}. ` : ''}${customerContext.topics_of_interest?.length ? `Previous interests: ${customerContext.topics_of_interest.join(', ')}. ` : ''}${customerContext.preferred_response_length ? `Preferred response length: ${customerContext.preferred_response_length}.` : ''}`
        };
      }

      const apiMessages: ChatCompletionMessageParam[] = [
        contextualSystemMessage,
        ...messages as ChatCompletionMessageParam[]
      ];

      // Call the Nebius API (OpenAI compatible)
      const response = await openai.chat.completions.create({
        model: process.env.NEBIUS_MODEL || 'Qwen/Qwen3-30B-A3B-fast',
        messages: apiMessages,
        temperature: 0.3, // Lower temperature for more deterministic responses
        max_tokens: 500
      });

      // Clean the response to remove any thinking text
      const rawMessage = response.choices[0].message.content || '';
      finalResponse = cleanResponseText(rawMessage);
      modelUsed = process.env.NEBIUS_MODEL || 'Qwen/Qwen3-30B-A3B-fast';
      tokensUsed = response.usage?.total_tokens || 0;
    }

    const responseTime = Date.now() - startTime;

    // Log the conversation to learning system
    try {
      // Create session if it doesn't exist
      if (!sessionId) {
        await learningService.createChatSession({
          session_id: currentSessionId,
          customer_identifier: customerIdentifier,
          conversation_outcome: 'ongoing'
        });
      }

      // Log user message
      if (userMessage) {
        await learningService.logMessage({
          session_id: currentSessionId,
          message_order: messages.length,
          role: userMessage.role,
          content: userMessage.content,
          timestamp: new Date()
        });
      }

      // Log assistant response
      await learningService.logMessage({
        session_id: currentSessionId,
        message_order: messages.length + 1,
        role: 'assistant',
        content: finalResponse,
        response_time_ms: responseTime,
        tokens_used: tokensUsed,
        model_used: modelUsed,
        timestamp: new Date()
      });

      // Update customer context if available
      if (customerIdentifier && userMessage) {
        const updatedContext = {
          customer_identifier: customerIdentifier,
          last_interaction: new Date(),
          interaction_history: {
            last_query: userMessage.content,
            response_time: responseTime,
            model_used: modelUsed
          }
        };

        await learningService.updateCustomerContext(updatedContext);
      }
    } catch (loggingError) {
      console.error('Error logging conversation:', loggingError);
      // Don't fail the request if logging fails
    }

    // Return the response with session info
    return res.json({
      message: finalResponse,
      sessionId: currentSessionId,
      responseTime,
      source: knowledgeResponse ? 'knowledge_base' : 'ai_model'
    });
  } catch (error) {
    console.error('Chat API error:', error);
    return res.status(500).json({
      error: `Error processing chat request: ${(error as Error).message}`
    });
  }
});

export default router;
