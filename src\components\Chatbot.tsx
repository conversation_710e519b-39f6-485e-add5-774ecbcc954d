import { useState, useEffect, useRef } from 'react';
import { MessageSquare, X, Send, Mic, MicOff, Volume2, VolumeX, Minimize2, Maximize2 } from 'lucide-react';
import chatService from '../services/chatService';
import { ttsService } from '../services/ttsService';
import { cleanTextForTTS, cleanTextForDisplay } from '../utils/textUtils';
import { ChatCompletionMessageParam } from 'openai/resources';

// Add type for SpeechRecognition
interface SpeechRecognitionEvent {
  results: {
    [key: number]: {
      [key: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start: () => void;
  stop: () => void;
  abort: () => void;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onend: (() => void) | null;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
}

interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
}

const Chatbot = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const [isListening, setIsListening] = useState(false);
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Initialize with a welcome message
  useEffect(() => {
    if (messages.length === 0) {
      setMessages([
        {
          role: 'assistant',
          content: 'Hello! I\'m SpireLab\'s virtual assistant. How can I help you with our IT solutions today?',
          timestamp: new Date()
        }
      ]);
    }
  }, [messages]);

  // Initialize speech synthesis
  useEffect(() => {
    // Check if TTS is available (either ElevenLabs or browser)
    const initializeTTS = async () => {
      if (ttsService.isElevenLabsAvailable()) {
        console.log('ElevenLabs TTS available - using realistic voice');
      } else {
        console.log('Using browser TTS as fallback');
      }
    };
    
    initializeTTS();
  }, []);

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Handle speech recognition
  useEffect(() => {
    let recognition: SpeechRecognition | null = null;
    
    if (isListening && (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition) {
      const SpeechRecognitionAPI = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      recognition = new SpeechRecognitionAPI() as SpeechRecognition;
      recognition.continuous = false;
      recognition.interimResults = false;
      
      recognition.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript;
        setInput(transcript);
        // Auto-send after a short delay
        setTimeout(() => {
          handleSendMessage(transcript);
        }, 500);
      };
      
      recognition.onend = () => {
        setIsListening(false);
      };
      
      recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Speech recognition error', event.error);
        setIsListening(false);
      };
      
      recognition.start();
    }
    
    return () => {
      if (recognition) {
        recognition.stop();
      }
    };
  }, [isListening]);

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
    if (!isOpen) {
      // Reset error state when opening
      setError(null);
    }
  };

  const toggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const toggleSpeech = () => {
    if (isSpeechEnabled) {
      ttsService.stop();
    }
    setIsSpeechEnabled(!isSpeechEnabled);
  };

  const toggleListening = () => {
    setIsListening(!isListening);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleSendMessage = async (text?: string) => {
    const messageText = text || input;
    if (!messageText.trim()) return;
    
    // Add user message to chat
    const userMessage: Message = {
      role: 'user',
      content: messageText,
      timestamp: new Date()
    };
    
    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);
    setError(null);
    
    try {
      // Convert messages to the format expected by the API
      const apiMessages: ChatCompletionMessageParam[] = messages
        .filter(msg => msg.role !== 'system')
        .map(msg => ({
          role: msg.role,
          content: msg.content
        }));
      
      // Add the new user message
      apiMessages.push({
        role: 'user',
        content: messageText
      });
      
      // Send to API with learning system integration
      const sessionId = `chatbot_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const response = await chatService.sendMessage(apiMessages, {
        sessionId,
        customerIdentifier: `chatbot_user_${sessionId}`
      });
      
      if (response.error) {
        setError(response.error);
      } else if (response.message) {
        // Clean the response text for display and TTS
        const displayText = cleanTextForDisplay(response.message);
        const ttsText = cleanTextForTTS(response.message);
        
        // Add assistant message to chat
        const assistantMessage: Message = {
          role: 'assistant',
          content: displayText,
          timestamp: new Date()
        };
        
        setMessages(prev => [...prev, assistantMessage]);
        
        // Speak the cleaned response if speech is enabled
        if (isSpeechEnabled) {
          ttsService.speak(ttsText, { speed: 0.9 });
        }
      }
    } catch (err) {
      setError(`An error occurred: ${(err as Error).message}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Format timestamp
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <>
      {/* Chat button */}
      <button
        onClick={toggleChatbot}
        className="fixed bottom-6 right-6 bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-full shadow-lg transition-all duration-300 z-50 flex items-center justify-center"
        aria-label="Chat with us"
      >
        {isOpen ? (
          <X size={24} />
        ) : (
          <MessageSquare size={24} />
        )}
      </button>

      {/* Chat window */}
      {isOpen && (
        <div 
          ref={chatContainerRef}
          className={`fixed bottom-20 right-6 bg-white dark:bg-gray-800 rounded-lg shadow-xl overflow-hidden z-50 transition-all duration-300 flex flex-col
            ${isExpanded ? 'w-[80vw] h-[80vh] max-w-4xl' : 'w-80 sm:w-96 h-[500px]'}`}
          style={{ 
            boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            border: '1px solid rgba(156, 39, 176, 0.3)'
          }}
        >
          {/* Chat header */}
          <div className="bg-purple-600 text-white p-4 flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <MessageSquare size={20} />
              <h3 className="font-medium">SpireLab Assistant</h3>
            </div>
            <div className="flex items-center space-x-2">
              <button 
                onClick={toggleSpeech}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label={isSpeechEnabled ? "Disable speech" : "Enable speech"}
                title={ttsService.isElevenLabsAvailable() ? "Using ElevenLabs realistic voice" : "Using browser voice"}
              >
                {isSpeechEnabled ? <Volume2 size={18} /> : <VolumeX size={18} />}
              </button>
              
              {'SpeechRecognition' in window || 'webkitSpeechRecognition' in window ? (
                <button 
                  onClick={toggleListening}
                  className={`text-white hover:text-gray-200 transition-colors ${isListening ? 'animate-pulse text-green-300' : ''}`}
                  aria-label={isListening ? "Stop listening" : "Start listening"}
                >
                  {isListening ? <Mic size={18} /> : <MicOff size={18} />}
                </button>
              ) : null}
              
              <button 
                onClick={toggleExpand}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label={isExpanded ? "Minimize chat" : "Expand chat"}
              >
                {isExpanded ? <Minimize2 size={18} /> : <Maximize2 size={18} />}
              </button>
              
              <button 
                onClick={toggleChatbot}
                className="text-white hover:text-gray-200 transition-colors"
                aria-label="Close chat"
              >
                <X size={18} />
              </button>
            </div>
          </div>

          {/* Chat messages */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {messages.map((message, index) => (
              <div 
                key={index} 
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div 
                  className={`max-w-[80%] rounded-lg p-3 ${
                    message.role === 'user' 
                      ? 'bg-purple-100 dark:bg-purple-900 text-gray-800 dark:text-gray-100' 
                      : 'bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-100'
                  }`}
                >
                  <p className="text-sm">{message.content}</p>
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1 text-right">
                    {formatTime(message.timestamp)}
                  </p>
                </div>
              </div>
            ))}
            {/* Loading indicator is hidden from user */}
            {error && (
              <div className="flex justify-center">
                <div className="bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded-lg p-3 max-w-[80%]">
                  <p className="text-sm">{error}</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Chat input */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center space-x-2">
              <input
                type="text"
                value={input}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                placeholder={isListening ? "Listening..." : "Type your message..."}
                disabled={isLoading || isListening}
                className="flex-1 border border-gray-300 dark:border-gray-600 rounded-full px-4 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500 dark:bg-gray-700 dark:text-white"
              />
              <button
                onClick={() => handleSendMessage()}
                disabled={isLoading || !input.trim()}
                className={`bg-purple-600 text-white p-2 rounded-full ${
                  isLoading || !input.trim() ? 'opacity-50 cursor-not-allowed' : 'hover:bg-purple-700'
                }`}
                aria-label="Send message"
              >
                <Send size={20} />
              </button>
            </div>
            {/* Voice selector is hidden by default as we auto-select the best voice */}
          </div>
        </div>
      )}
    </>
  );
};

export default Chatbot;
