import React, { useState, useEffect, useRef } from 'react';
import { X, Send, Mic, MicOff, Volume2, VolumeX, Minimize2, Maximize2, <PERSON><PERSON>, User, UserPlus } from 'lucide-react';
import chatService from '../services/chatService';
import { ttsService } from '../services/ttsService';
import { cleanTextForTTS, cleanTextForDisplay } from '../utils/textUtils';
import { ChatCompletionMessageParam } from 'openai/resources';
import SiriWaveAvatar from './SiriWaveAvatar';
import ChatbotContactForm, { ContactFormData } from './ChatbotContactForm';
import contactService from '../services/contactService';
import './FuturisticHumanoidChatbot.css';

// Speech Recognition interfaces
interface SpeechRecognitionEvent {
  results: {
    [key: number]: {
      [key: number]: {
        transcript: string;
        confidence: number;
      };
    };
  };
}

interface SpeechRecognitionErrorEvent {
  error: string;
  message?: string;
}

interface SpeechRecognition extends EventTarget {
  continuous: boolean;
  interimResults: boolean;
  lang: string;
  start(): void;
  stop(): void;
  abort(): void;
  onresult: ((event: SpeechRecognitionEvent) => void) | null;
  onerror: ((event: SpeechRecognitionErrorEvent) => void) | null;
  onstart: (() => void) | null;
  onend: (() => void) | null;
}

declare global {
  interface Window {
    SpeechRecognition: new () => SpeechRecognition;
    webkitSpeechRecognition: new () => SpeechRecognition;
  }
}

interface Message {
  id: string;
  text: string;
  isUser: boolean;
  timestamp: Date;
}

type HumanoidEmotion = 'idle' | 'thinking' | 'speaking' | 'happy' | 'curious' | 'excited' | 'listening' | 'processing';

const FuturisticHumanoidChatbot: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isSpeechEnabled, setIsSpeechEnabled] = useState(true);
  const [isListening, setIsListening] = useState(false);
  const [recognition, setRecognition] = useState<SpeechRecognition | null>(null);
  const [currentEmotion, setCurrentEmotion] = useState<HumanoidEmotion>('idle');
  const [isTyping, setIsTyping] = useState(false);
  const [showContactForm, setShowContactForm] = useState(false);
  const [chatSessionId] = useState(() => contactService.generateChatSessionId());
  const [contactInfo, setContactInfo] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    hasAskedName: false,
    hasAskedEmail: false,
    hasAskedPhone: false,
    isCollectingInfo: false,
    currentStep: 'none' as 'none' | 'name' | 'email' | 'phone' | 'company' | 'complete'
  });

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined' && (window.SpeechRecognition || window.webkitSpeechRecognition)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      const recognitionInstance = new SpeechRecognition();
      
      recognitionInstance.continuous = false;
      recognitionInstance.interimResults = false;
      recognitionInstance.lang = 'en-US';

      recognitionInstance.onresult = (event: SpeechRecognitionEvent) => {
        const transcript = event.results[0][0].transcript;
        setInputText(transcript);
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      recognitionInstance.onerror = (event: SpeechRecognitionErrorEvent) => {
        console.error('Speech recognition error:', event.error);
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      recognitionInstance.onend = () => {
        setIsListening(false);
        setCurrentEmotion('idle');
      };

      setRecognition(recognitionInstance);
    }
  }, []);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Welcome message and start contact collection
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      const welcomeMessage: Message = {
        id: 'welcome',
        text: 'Hello! I\'m ARIA, your advanced AI assistant from SpireLab. I\'m here to help you with all your IT needs. To provide you with the best personalized service, may I start by getting your name? (You can say "skip contact" if you prefer to remain anonymous)',
        isUser: false,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setCurrentEmotion('happy');
      setContactInfo(prev => ({ ...prev, isCollectingInfo: true, currentStep: 'name' }));

      setTimeout(() => {
        setCurrentEmotion('idle');
      }, 3000);
    }
  }, [isOpen]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      text: inputText.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputText.trim();
    setInputText('');
    setIsLoading(true);
    setCurrentEmotion('thinking');
    setIsTyping(true);

    try {
      // Check if we're collecting contact information
      if (contactInfo.isCollectingInfo && contactInfo.currentStep !== 'complete') {
        const collectionResult = handleContactInfoCollection(currentInput);

        if (!collectionResult.shouldContinue && collectionResult.responseMessage) {
          // Simulate typing effect for contact collection response
          setTimeout(() => {
            const assistantMessage: Message = {
              id: (Date.now() + 1).toString(),
              text: collectionResult.responseMessage!,
              isUser: false,
              timestamp: new Date()
            };

            setMessages(prev => [...prev, assistantMessage]);
            setIsLoading(false);
            setIsTyping(false);
            setCurrentEmotion('speaking');

            // Text-to-speech
            if (isSpeechEnabled) {
              const ttsText = cleanTextForTTS(collectionResult.responseMessage!);
              ttsService.speak(ttsText);
            }

            setTimeout(() => {
              setCurrentEmotion('idle');
            }, 2000);
          }, 800);
          return;
        }
      }

      // Regular AI chat processing
      const chatMessages: ChatCompletionMessageParam[] = messages.map(msg => ({
        role: msg.isUser ? 'user' : 'assistant',
        content: msg.text
      }));

      chatMessages.push({
        role: 'user',
        content: userMessage.text
      });

      // Include session ID and customer identifier for learning system
      const response = await chatService.sendMessage(chatMessages, {
        sessionId: chatSessionId,
        customerIdentifier: contactInfo.email || contactInfo.name || `anonymous_${chatSessionId}`
      });

      if (response.error) {
        throw new Error(response.error);
      }

      const cleanedResponse = cleanTextForDisplay(response.message);

      // Simulate typing effect
      setTimeout(() => {
        const assistantMessage: Message = {
          id: (Date.now() + 1).toString(),
          text: cleanedResponse,
          isUser: false,
          timestamp: new Date()
        };

        setMessages(prev => [...prev, assistantMessage]);
        setIsLoading(false);
        setIsTyping(false);
        setCurrentEmotion('speaking');

        // Text-to-speech
        if (isSpeechEnabled) {
          const ttsText = cleanTextForTTS(cleanedResponse);
          ttsService.speak(ttsText);
        }

        setTimeout(() => {
          setCurrentEmotion('idle');
        }, 2000);
      }, 1000);

    } catch (error) {
      console.error('Error sending message:', error);
      setError('Failed to send message. Please try again.');
      setIsLoading(false);
      setIsTyping(false);
      setCurrentEmotion('idle');
    }
  };

  const handleVoiceInput = () => {
    if (!recognition) return;

    if (isListening) {
      recognition.stop();
      setIsListening(false);
      setCurrentEmotion('idle');
    } else {
      recognition.start();
      setIsListening(true);
      setCurrentEmotion('listening');
    }
  };

  const toggleSpeech = () => {
    setIsSpeechEnabled(!isSpeechEnabled);
    if (!isSpeechEnabled) {
      ttsService.stop();
    }
  };

  const handleContactInfoCollection = (userInput: string): { shouldContinue: boolean; responseMessage?: string } => {
    const input = userInput.trim().toLowerCase();

    // Allow users to skip contact collection entirely
    if (input.includes('skip contact') || input.includes('skip this') || input.includes('no thanks')) {
      setContactInfo(prev => ({
        ...prev,
        isCollectingInfo: false,
        currentStep: 'complete'
      }));
      return {
        shouldContinue: false,
        responseMessage: "No problem! I'm here to help with any questions you have about our IT services. What can I assist you with today?"
      };
    }

    switch (contactInfo.currentStep) {
      case 'name':
        // Check if it's a greeting or generic response that's not a name
        const greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon', 'good evening', 'how are you'];
        const isGreeting = greetings.some(greeting => input.includes(greeting));
        const isQuestion = userInput.includes('?');
        const isGenericResponse = ['yes', 'ok', 'okay', 'sure', 'fine', 'good'].includes(input);

        if (isGreeting || isQuestion || isGenericResponse) {
          return {
            shouldContinue: false,
            responseMessage: "Hello! I appreciate the greeting. To get started, could you please tell me your name? For example, 'My name is John' or just 'John Smith'."
          };
        }

        // Check if it looks like a name (at least 2 characters, not all numbers)
        const trimmedInput = userInput.trim();
        const hasNumbers = /\d/.test(trimmedInput);
        const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(trimmedInput);

        if (trimmedInput.length >= 2 && !hasNumbers && !hasSpecialChars) {
          // Clean up common name prefixes
          let cleanName = trimmedInput;
          const namePrefixes = ['my name is ', 'i am ', 'i\'m ', 'call me ', 'it\'s '];
          for (const prefix of namePrefixes) {
            if (cleanName.toLowerCase().startsWith(prefix)) {
              cleanName = cleanName.substring(prefix.length).trim();
              break;
            }
          }

          if (cleanName.length >= 2) {
            setContactInfo(prev => ({
              ...prev,
              name: cleanName,
              hasAskedName: true,
              currentStep: 'email'
            }));
            return {
              shouldContinue: false,
              responseMessage: `Nice to meet you, ${cleanName}! To ensure we can follow up with you properly, could you please provide your email address?`
            };
          }
        }

        return {
          shouldContinue: false,
          responseMessage: "I'd love to know your name so I can assist you better. Please share your first name or full name with me."
        };

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (emailRegex.test(userInput.trim())) {
          setContactInfo(prev => ({
            ...prev,
            email: userInput.trim(),
            hasAskedEmail: true,
            currentStep: 'phone'
          }));
          return {
            shouldContinue: false,
            responseMessage: `Thank you! And could you share your phone number? This helps us provide better support if needed. (You can say "skip" if you prefer not to share)`
          };
        } else {
          return {
            shouldContinue: false,
            responseMessage: "Please provide a valid email address so we can stay in touch with you."
          };
        }

      case 'phone':
        const phoneRegex = /^[\+]?[1-9][\d\s\-\(\)]{7,15}$/;
        if (phoneRegex.test(userInput.replace(/[\s\-\(\)]/g, '')) || input.includes('skip') || input.includes('no')) {
          const phoneNumber = input.includes('skip') || input.includes('no') ? '' : userInput.trim();
          setContactInfo(prev => ({
            ...prev,
            phone: phoneNumber,
            hasAskedPhone: true,
            currentStep: 'company'
          }));
          return {
            shouldContinue: false,
            responseMessage: `Great! One last question - what company do you work for? (You can say "skip" if you prefer not to share)`
          };
        } else {
          return {
            shouldContinue: false,
            responseMessage: `Please provide a valid phone number, or say "skip" if you'd prefer not to share it.`
          };
        }

      case 'company':
        const company = input.includes('skip') || input.includes('no') ? '' : userInput.trim();

        // Save contact information to database with all collected data
        const finalContactData = {
          name: contactInfo.name,
          email: contactInfo.email,
          phone: contactInfo.phone,
          company: company
        };

        setContactInfo(prev => ({
          ...prev,
          company,
          currentStep: 'complete',
          isCollectingInfo: false
        }));

        // Save contact information to database
        saveContactInfo(finalContactData);

        return {
          shouldContinue: false,
          responseMessage: `Perfect! Thank you ${contactInfo.name}. I have all your information and we're ready to help. Now, how can I assist you with your IT needs today?`
        };

      default:
        return { shouldContinue: true };
    }
  };

  const saveContactInfo = async (collectedData: { name: string; email: string; phone: string; company: string }) => {
    try {
      console.log('Saving contact info:', collectedData); // Debug log

      const contactData: ContactFormData = {
        name: collectedData.name,
        email: collectedData.email,
        phone: collectedData.phone,
        company: collectedData.company,
        message: 'Contact information collected via chat interaction',
        inquiry_type: 'general',
        preferred_contact_method: 'email',
        chat_session_id: chatSessionId
      };

      const response = await contactService.submitContact(contactData);
      if (response.success) {
        console.log('Contact info saved successfully:', response);
      } else {
        console.error('Failed to save contact info:', response.message);
      }
    } catch (error) {
      console.error('Error saving contact info:', error);
    }
  };

  const handleContactFormSubmit = async (contactData: ContactFormData) => {
    try {
      const response = await contactService.submitContact({
        ...contactData,
        chat_session_id: chatSessionId
      });

      if (response.success) {
        // Add a system message to the chat
        const systemMessage: Message = {
          id: `contact_${Date.now()}`,
          text: "Thank you for your contact information! We'll get back to you soon. Is there anything else I can help you with?",
          isUser: false,
          timestamp: new Date()
        };
        setMessages(prev => [...prev, systemMessage]);

        // Speak the confirmation if speech is enabled
        if (isSpeechEnabled) {
          const ttsText = cleanTextForTTS(systemMessage.text);
          ttsService.speak(ttsText);
        }
      } else {
        throw new Error(response.message || 'Failed to submit contact information');
      }
    } catch (error) {
      console.error('Error submitting contact form:', error);
      setError('Failed to submit contact information. Please try again.');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getEmotionClass = () => {
    switch (currentEmotion) {
      case 'thinking': return 'thinking';
      case 'speaking': return 'speaking';
      case 'happy': return 'happy';
      case 'excited': return 'excited';
      case 'curious': return 'curious';
      case 'listening': return 'listening';
      case 'processing': return 'processing';
      default: return 'idle';
    }
  };

  if (!isOpen) {
    return (
      <div className="humanoid-chatbot-trigger">
        <button
          onClick={() => setIsOpen(true)}
          className="humanoid-trigger-button"
          aria-label="Open AI Assistant"
        >
          <div className="siri-trigger-avatar">
            <SiriWaveAvatar
              isListening={false}
              isSpeaking={false}
              isThinking={false}
              emotion="idle"
              size={60}
            />
          </div>
          <div className="trigger-pulse"></div>
        </button>
      </div>
    );
  }

  return (
    <div className={`humanoid-chatbot ${isExpanded ? 'expanded' : ''}`}>
      <div className="humanoid-container" ref={chatContainerRef}>
        {/* Header with Shader Circle Avatar */}
        <div className="humanoid-header">
          <div className="siri-avatar-container">
            <SiriWaveAvatar
              isListening={isListening}
              isSpeaking={isSpeechEnabled && !isLoading && !isTyping}
              isThinking={isLoading || isTyping}
              emotion={currentEmotion}
              size={120}
            />

            {/* Status Indicators */}
            <div className="status-indicators">
              <div className={`status-light ${isListening ? 'listening' : ''}`}></div>
              <div className={`status-light ${isLoading ? 'processing' : ''}`}></div>
              <div className={`status-light ${isSpeechEnabled ? 'speech-enabled' : ''}`}></div>
            </div>
          </div>
          
          {/* Header Info */}
          <div className="header-info">
            <h3 className="ai-name">ARIA</h3>
            <p className="ai-title">Advanced Responsive Intelligence Assistant</p>
            <div className="ai-status">
              {isLoading ? 'Processing...' :
               isListening ? 'Listening...' :
               isTyping ? 'Typing...' :
               contactInfo.isCollectingInfo ? `Getting ${contactInfo.currentStep}...` :
               'Online'}
            </div>
            {contactInfo.isCollectingInfo && (
              <div className="contact-progress">
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{
                      width: `${(
                        (contactInfo.hasAskedName ? 1 : 0) +
                        (contactInfo.hasAskedEmail ? 1 : 0) +
                        (contactInfo.hasAskedPhone ? 1 : 0) +
                        (contactInfo.currentStep === 'complete' ? 1 : 0)
                      ) * 25}%`
                    }}
                  />
                </div>
                <span className="progress-text">Setting up your profile...</span>
              </div>
            )}
          </div>
          
          {/* Header Controls */}
          <div className="header-controls">
            <button
              onClick={() => setShowContactForm(true)}
              className="control-btn contact-btn"
              aria-label="Contact Us"
              title="Get in touch with us"
            >
              <UserPlus size={16} />
            </button>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="control-btn"
              aria-label={isExpanded ? 'Minimize' : 'Maximize'}
            >
              {isExpanded ? <Minimize2 size={16} /> : <Maximize2 size={16} />}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="control-btn close-btn"
              aria-label="Close"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        {/* Messages Area */}
        <div className="messages-container">
          <div className="messages-list">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.isUser ? 'user-message' : 'ai-message'}`}
              >
                <div className="message-avatar">
                  {message.isUser ? (
                    <User size={20} />
                  ) : (
                    <div className="ai-message-avatar">
                      <Bot size={20} />
                      <div className="avatar-glow"></div>
                    </div>
                  )}
                </div>
                <div className="message-content">
                  <div className="message-text" style={{ whiteSpace: 'pre-wrap' }}>
                    {message.text}
                  </div>
                  <div className="message-time">
                    {message.timestamp.toLocaleTimeString([], {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="message ai-message typing-indicator">
                <div className="message-avatar">
                  <div className="ai-message-avatar">
                    <Bot size={20} />
                    <div className="avatar-glow"></div>
                  </div>
                </div>
                <div className="message-content">
                  <div className="typing-animation">
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                    <div className="typing-dot"></div>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="input-container">
          <div className="input-wrapper">
            <input
              ref={inputRef}
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type your message..."
              className="message-input"
              disabled={isLoading}
            />
            
            <div className="input-controls">
              <button
                onClick={handleVoiceInput}
                className={`control-btn voice-btn ${isListening ? 'listening' : ''}`}
                aria-label={isListening ? 'Stop listening' : 'Start voice input'}
                disabled={!recognition}
              >
                {isListening ? <MicOff size={18} /> : <Mic size={18} />}
              </button>
              
              <button
                onClick={toggleSpeech}
                className={`control-btn speech-btn ${isSpeechEnabled ? 'enabled' : ''}`}
                aria-label={isSpeechEnabled ? 'Disable speech' : 'Enable speech'}
              >
                {isSpeechEnabled ? <Volume2 size={18} /> : <VolumeX size={18} />}
              </button>
              
              <button
                onClick={handleSendMessage}
                className="send-btn"
                disabled={!inputText.trim() || isLoading}
                aria-label="Send message"
              >
                <Send size={18} />
              </button>
            </div>
          </div>
          
          {error && (
            <div className="error-message">
              {error}
              <button onClick={() => setError(null)} className="error-close">×</button>
            </div>
          )}
        </div>
      </div>

      {/* Contact Form Modal */}
      <ChatbotContactForm
        isOpen={showContactForm}
        onClose={() => setShowContactForm(false)}
        onSubmit={handleContactFormSubmit}
        chatSessionId={chatSessionId}
      />
    </div>
  );
};

export default FuturisticHumanoidChatbot;
